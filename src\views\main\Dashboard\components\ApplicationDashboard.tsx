import React from 'react';
import { Card, Col, Row, Statistic, Tag } from 'antd';
import {
  Bar,
  Pie,
  Line
} from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import dayjs from 'dayjs';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  TimeScale
);

export const ApplicationDashboard: React.FC = () => {
  // Gi<PERSON> lập dữ liệu thống kê
  const newUsersThisWeek = 5;
  const inactiveUsers = 500000000;
  const lockedUsers = 6000000;
  const avgActiveTimeMins = 1;

  const roleDistribution = {
    labels: ['Nhân viên', '<PERSON><PERSON>h nghiệp'],
    datasets: [
      {
        label: 'Tỷ lệ người dùng theo vai trò',
        data: [50, 5],
        backgroundColor: [ '#52c41a', '#faad14'],
      },
    ],
  };

  const userGrowthData = {
    labels: Array.from({ length: 7 }, (_, i) =>
      dayjs().subtract(6 - i, 'day').format('DD/MM')
    ),
    datasets: [
      {
        label: 'Người dùng mới',
        data: [2, 3, 4, 5, 4, 6, 4],
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        fill: false,
        tension: 0.4,
      },
    ],
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic title="Số lượng ứng dụng" value={newUsersThisWeek} suffix="ứng dụng" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Tổng doanh thu" value={inactiveUsers} suffix="VNĐ" />
          </Card>
          
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Doanh thu hằng tháng" value={lockedUsers} suffix="VNĐ" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Ứng dụng ngừng hoạt động" value={avgActiveTimeMins} suffix="ứng dụng" />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
