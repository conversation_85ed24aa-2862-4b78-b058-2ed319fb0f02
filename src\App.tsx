import { FC, Suspense, useEffect } from "react";
import { ConfigProvider, Spin, theme } from "antd";

import moment, { locale } from "moment";
import { BrowserRouter } from "react-router-dom";
import AppRouter from "./views/AppRouter";
import ActivityIndicator from "./components/ActivityIndicator";
import { useThemeStore } from "./stores/themeStore";
import { useAuthStore } from "./stores/authStore";
import { IntlProvider } from "react-intl";
import { localeConfig, LocaleFormatter } from "./locales";
import { LOCALE_RESOURCE } from "./locales/locale.resource";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

interface IProps {}
const App: FC<IProps> = (props: IProps) => {
  const { antdValue, momentValue } = LOCALE_RESOURCE["en"];
  const { changeTheme } = useThemeStore();
  const { authenticate, isFirstLoading } = useAuthStore();
  useEffect(() => {
    moment.locale(momentValue);
    authenticate();
  }, [authenticate, changeTheme, momentValue]);

  const { themConfig } = useThemeStore();
  const queryClient = new QueryClient();
  return (
    <ConfigProvider
      theme={{
        ...themConfig,
      }}
      locale={antdValue}
    >
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={"en"} messages={localeConfig["en_US"]}>
          <Suspense fallback={null}>
            <Spin
              spinning={isFirstLoading}
              wrapperClassName="app-loading-wrapper"
              tip={<LocaleFormatter id="gloabal.tips.loading" />}
            >
              {isFirstLoading ? <ActivityIndicator /> : <AppRouter />}
            </Spin>
          </Suspense>
        </IntlProvider>
      </QueryClientProvider>
    </ConfigProvider>
  );
};
export default App;
