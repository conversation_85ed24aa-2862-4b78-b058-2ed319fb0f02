import {
  DashboardOutlined,
  TeamOutlined,
  DesktopOutlined,
  FundViewOutlined,
  ShopOutlined,
  SettingOutlined,
  GroupOutlined,
} from "@ant-design/icons";
import { IRouter } from "~/routers";
import { Dashboard } from "./Dashboard";
import { TenantView } from "./Tenant";
import { Application } from "./Application";
import { Account } from "./Account";
//import { ReportView } from "./Report";
import {Revenua} from "./Revenua"; // Assuming you have a Revenua component
import { SettingView } from "./Setting";
import { UserSettingView } from "./Setting/components/UserSettingView";
import { Group } from "antd/es/avatar";
const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children,
});

export const mainRouter: IRouter[] = [
  createRoute(
    "/dashboard",
    <Dashboard />,
    "Dashboard",
    <DashboardOutlined />
  ),
  createRoute(
    "/list-tenant",
    <TenantView  />,
    "Tenant",
    <ShopOutlined />
  ),
  createRoute(
    "/list-application",
    <Application />,
    "Applications",
    <DesktopOutlined />
  ),
  createRoute(
    "/list-account",
    <Account />,
    "Accounts",
    <TeamOutlined />
  ),
  // 
  // createRoute(
  //   "/report",
  //   <Report />,
  //   "Reports",
  //   <FundViewOutlined />
  // ),
  createRoute(
    "/revenua",
    <Revenua />,
    "Revenua & Bill",
    <FundViewOutlined />
  ),
  createRoute(
    "/setting",
    <SettingView />,
    "Setting",
    <SettingOutlined />,
    true,
    [
      createRoute(
        "setting/user",
        <UserSettingView />,
        "System User",
        <TeamOutlined />
      ),
    ]
  ),
];
