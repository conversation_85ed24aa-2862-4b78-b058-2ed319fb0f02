# Admin Web - React TypeScript Admin Dashboard

Hệ thống quản trị doanh nghiệp được xây dựng với React, TypeScript, Ant Design và TanStack Query.

## 🚀 Tính năng chính
- **Quản lý khách hàng**: CRUD operations, filter, pagination
- **Đa ngôn ngữ**: Hỗ trợ Tiếng Việt, English, 中文
- **Responsive Design**: Tối ưu cho mọi thiết bị

## 🛠 Tech Stack

- **Frontend**: React 18, TypeScript
- **UI Library**: Ant Design 5.x
- **State Management**: TanStack Query (React Query)
- **Routing**: React Router DOM v6
- **Build Tool**: Create React App + CRACO
- **Styling**: Less, CSS-in-JS
- **Charts**: Chart.js, Recharts
- **Icons**: Ant Design Icons

## 📁 Cấu trúc thư mục

```
src/
├── components/           # Shared components
│   ├── BaseText/        # Text component với variants
│   ├── BaseTable/       # Table component với pagination
│   ├── BaseView/        # Layout wrapper
│   └── BaseModal/       # Modal component
├── views/               # Page components
│   └── main/
│       ├── Customer/    # Customer management
├── hooks/               # Custom React hooks
│   ├── customer/        # Customer related hooks
├── services/            # API services
│   ├── @common/         # Common services
│   └── endpoints.ts     # API endpoints
├── dto/                 # TypeScript interfaces
├── locales/             # Internationalization
├── data/                # Mock data & constants
└── styles/              # Global styles
```

## 🚦 Cài đặt và chạy

### Prerequisites
- Node.js >= 16
- Yarn hoặc npm

### Installation

```bash
# Clone repository
git clone <repository-url>
cd admin-web

# Install dependencies
yarn install

# Start development server
yarn start
```

### Available Scripts

```bash
# Development
yarn start          # Start dev server (localhost:3000)
yarn dev           # Start with dev environment
yarn sandbox      # Start with sandbox environment
yarn prod         # Start with production environment

# Build
yarn build         # Production build
yarn build:dev     # Build for dev environment
yarn build:prod    # Build for production
yarn build:sandbox # Build for sandbox

# Testing
yarn test          # Run tests
```

## 🔧 Environment Configuration

Tạo file `.env` trong thư mục root:

```env
REACT_APP_ENV=dev
REACT_APP_API_URL=http://localhost:8080
REACT_APP_VERSION=1.0.0
```

## 📋 Các tính năng chính

### 1. Quản lý khách hàng
- **Danh sách**: Filter theo tên, phone, email với pagination
- **Thêm mới**: Form tạo khách hàng với validation
- **Chỉnh sửa**: Cập nhật thông tin khách hàng
- **Xóa**: Xóa khách hàng với confirmation
- **Chi tiết**: Xem thông tin chi tiết và copy thông tin

### 2. Components tái sử dụng

#### BaseText Component
```tsx
import { BaseText } from '@/components';

<BaseText variant="h1" color="primary" weight="bold">
  Heading Text
</BaseText>
```

#### BaseTable Component
```tsx
import BaseTable from '~/components/BaseTable';

<BaseTable
  columns={columns}
  data={data}
  total={total}
  isLoading={isLoading}
  onPageChange={handlePageChange}
/>
```

### 3. Custom Hooks

## 🔒 Authentication & Authorization

```tsx
// Token management
import { authService } from '~/services/@common';

// Get token
const token = authService.getAccessToken();

// API interceptor
interceptors: {
  [KeyHeader.AUTHORIZATION]: async () => {
    const token = authService.getAccessToken();
    return `Bearer ${token}`;
  },
}
```

## 📱 Responsive Design

- **Mobile First**: Thiết kế ưu tiên mobile
- **Breakpoints**: xs, sm, md, lg, xl, xxl
- **Grid System**: Ant Design Grid với gutter

```tsx
<Row gutter={[16, 16]}>
  <Col xs={24} sm={12} md={8} lg={6}>
    Content
  </Col>
</Row>
```

## 🚀 Deployment

### Production Build
```bash
yarn build:prod
```

### Deploy Script
```bash
# Auto deploy to server
./deploy-prod.sh
```

Script sẽ:
1. Build production
2. Tạo zip file
3. Upload lên server
4. Extract và deploy

## 🧪 Testing

```bash
# Run tests
yarn test

# Run tests with coverage
yarn test --coverage
```

## 📈 Performance Optimization

- **Code Splitting**: Lazy loading components
- **Memoization**: React.memo, useMemo, useCallback
- **Bundle Analysis**: Webpack Bundle Analyzer
- **Image Optimization**: WebP format
- **Caching**: TanStack Query caching strategy

## 🔍 Debugging

### Development Tools
- React Developer Tools
- TanStack Query Devtools
- Redux DevTools (nếu sử dụng)

### Logging
```tsx
// Toast notifications
import { toastService } from '~/services';

toastService.success('Thành công!');
toastService.error('Có lỗi xảy ra!');
toastService.warning('Cảnh báo!');
```

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -m 'Add new feature'`
4. Push branch: `git push origin feature/new-feature`
5. Tạo Pull Request

## 📝 Code Style

- **ESLint**: Code linting
- **Prettier**: Code formatting
- **TypeScript**: Type safety
- **Naming Convention**: camelCase, PascalCase

## 🐛 Troubleshooting

### Common Issues

1. **Build errors**: Xóa `node_modules` và `yarn.lock`, chạy lại `yarn install`
2. **Port conflicts**: Thay đổi port trong package.json
3. **API errors**: Kiểm tra environment variables
4. **Style issues**: Clear browser cache

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: [Link to docs]
- **Issues**: [GitHub Issues]

## 📄 License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.

---

**Version**: 0.1.0  
**Last Updated**: 2024-01-01  
**Maintainer**: Development Team
