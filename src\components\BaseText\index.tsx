import React from "react";
import { Typography } from "antd";

const { Text } = Typography;

export interface BaseTextProps {
  children?: React.ReactNode;
  variant?:
    | "body1"
    | "body2"
    | "caption"
    | "subtitle1"
    | "subtitle2"
    | "h1"
    | "h2"
    | "h3"
    | "h4"
    | "h5"
    | "h6";
  color?:
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "error"
    | "info"
    | "textPrimary"
    | "textSecondary";
  weight?: "light" | "normal" | "medium" | "semibold" | "bold";
  size?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "3xl";
  align?: "left" | "center" | "right" | "justify";
  transform?: "none" | "capitalize" | "uppercase" | "lowercase";
  decoration?: "none" | "underline" | "line-through";
  truncate?: boolean;
  maxLines?: number;
  style?: React.CSSProperties;
  className?: string;
  onClick?: () => void;
  id?: string;
}

const BaseText: React.FC<BaseTextProps> = ({
  children,
  variant = "body1",
  color = "textPrimary",
  weight = "normal",
  size,
  align = "left",
  transform = "none",
  decoration = "none",
  truncate = false,
  maxLines,
  style,
  className,
  onClick,
  id,
  ...restProps
}) => {
  // Xử lý size based on variant
  const getSize = () => {
    if (size) {
      const sizeMap = {
        xs: "12px",
        sm: "14px",
        md: "16px",
        lg: "18px",
        xl: "20px",
        "2xl": "24px",
        "3xl": "32px",
      };
      return sizeMap[size];
    }

    // Default sizes based on variant
    const variantSizeMap = {
      h1: "32px",
      h2: "28px",
      h3: "24px",
      h4: "20px",
      h5: "18px",
      h6: "16px",
      subtitle1: "16px",
      subtitle2: "14px",
      body1: "16px",
      body2: "14px",
      caption: "12px",
    };
    return variantSizeMap[variant];
  };

  // Xử lý font weight
  const getFontWeight = () => {
    const weightMap = {
      light: 200,
      normal: 300,
      medium: 40,
      semibold: 500,
      bold: 600,
    };
    return weightMap[weight];
  };

  // Xử lý color
  const getColor = () => {
    const colorMap = {
      primary: "#1890ff",
      secondary: "#722ed1",
      success: "#52c41a",
      warning: "#faad14",
      error: "#ff4d4f",
      info: "#1890ff",
      textPrimary: "rgba(0, 0, 0, 0.85)",
      textSecondary: "rgba(0, 0, 0, 0.65)",
    };
    return colorMap[color];
  };

  // Xử lý ellipsis cho truncate
  const getEllipsisConfig = () => {
    if (truncate) {
      return true;
    }
    return false;
  };

  const customStyle: React.CSSProperties = {
    fontSize: getSize(),
    fontWeight: getFontWeight(),
    textAlign: align,
    textTransform: transform,
    textDecoration: decoration,
    margin: 0,
    // Handle maxLines with CSS for multi-line truncation
    ...(truncate &&
      maxLines && {
        display: "-webkit-box",
        WebkitLineClamp: maxLines,
        WebkitBoxOrient: "vertical" as const,
        overflow: "hidden",
      }),
    ...style,
  };

  return (
    <Text
      style={customStyle}
      className={className}
      ellipsis={getEllipsisConfig()}
      onClick={onClick}
      id={id}
      {...restProps}
    >
      {children}
    </Text>
  );
};

export default BaseText;
