export const endpoints_dashboard = {
  stats: "/api/admin/dashboard/stats".trim(),
  topTenants: "/api/admin/dashboard/top-tenants".trim(),
  overview: "/api/admin/dashboard/overview".trim(),
  growth: "/api/admin/dashboard/growth".trim(),
};

export const endpoints_tenant = {
  listTenant: "/api/admin/tenants/list".trim(),
  createTenant: "/api/admin/tenants/register".trim(),
  updateTenant: "/api/admin/tenants/update".trim(),
  detailTenant: "/api/admin/tenants/details".trim(),
  activeTenant: "/api/admin/tenants/active".trim(),
  inactiveTenant: "/api/admin/tenant/inactive".trim(),
  informationTenant: "/api/admin/tenants/information".trim(),
};

export const endpoints_application = {
  createApplication: "/api/admin/applications/create".trim(),
  updateApplication: "/api/admin/applications/update".trim(),
  listApplication: "/api/admin/applications/list".trim(),
  getApplication: "/api/admin/applications/get".trim(),
  activeApplication: "/api/admin/applications/active".trim(),
  inactiveApplication: "/api/admin/applications/inactive".trim(),
};

export const endpoints_account = {
  getAccount: "/api/admin/accounts/detail".trim(),
  updateAccount: "/api/admin/accounts/update".trim(),
  changePassword: "/api/admin/accounts/reset-password".trim(),
  listAccounts: "/api/admin/accounts/list".trim(),
  createAccount: "/api/admin/accounts/create".trim(),
  activeAccount: "/api/admin/accounts/active".trim(),
  inactiveAccount: "/api/admin/accounts/inactive".trim(),
};

export const endpoints_user_admin = {
  getAccount: "/api/admin/auth/detail".trim(),
  updateAccount: "/api/admin/auth/update".trim(),
  changePassword: "/api/admin/auth/reset-password".trim(),
  listAccounts: "/api/admin/auth/list".trim(),
  createAccount: "/api/admin/auth/create".trim(),
  activeAccount: "/api/admin/auth/active".trim(),
  inactiveAccount: "/api/admin/auth/inactive".trim(),
};

export const endpoints_report = {};

export const endpoints_upload = {
  uploadSingle: "/api/admin/upload/upload_single_s3".trim(),
  uploadMutiple: "/api/admin/upload/upload_mutiple_s3".trim(),
};
