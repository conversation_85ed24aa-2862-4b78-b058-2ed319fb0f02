import { useMutation, useQuery } from "@tanstack/react-query";
import { rootApiService, toastService } from "~/services/@common";
import { endpoints_tenant } from "~/services/endpoints";
import { useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import { ListTenantReq, ListTenantRes } from "~/dto/tenant.dto";
import { notification } from "antd";
import { useEffect, useState } from "react";
export interface TenantResponse {
  tenantInfos?: TenantAccount[];
  activeAccount?: number;
  activeThisMonth?: number;
  inActiveAccount?: number;
  totalAccount?: number;
}

export interface TenantAccount {
  avatar?: string;
  createdBy?: string;
  createdDate?: string;
  fullName?: string;
  id?: string;
  status?: string;
  tenantId?: string;
  type?: string;
  updatedBy?: string;
  updatedDate?: string;
  username?: string;
}

const useTenant = (
  params?: Partial<ListTenantReq>,
  enabled: boolean = false
) => {
  const queryClient = useQueryClient();
  const [details, setDetails] = useState<any>(null);
  const [isDetailLoading, setIsDetailLoading] = useState(false);

  const { data, isLoading, refetch } = useInfiniteQuery<ListTenantRes, Error>({
    queryKey: [endpoints_tenant.listTenant, params],
    queryFn: () => {
      return rootApiService.get<ListTenantRes>(
        endpoints_tenant.listTenant,
        params
      );
    },
    getNextPageParam: (lastPage, allPages) =>
      lastPage.data.length > 0 ? allPages.length + 1 : undefined,
    initialPageParam: 1,
    enabled: enabled,
  });

  const formatData = data?.pages.flatMap((page) => page.data) ?? [];
  const total = data?.pages[0]?.total ?? 0;

  // ✅ Get Tenant Details thủ công
  const getDetails = async ({ tenantId }: { tenantId: string }) => {
    try {
      setIsDetailLoading(true);
      const response = await rootApiService.get(endpoints_tenant.detailTenant, {
        tenantId,
      });
      setDetails(response); // hoặc response.data nếu có dạng đó
      setIsDetailLoading(false);
    } catch (error) {
      toastService.error("Lấy thông tin chi tiết thất bại");
    } finally {
      setIsDetailLoading(false);
    }
  };

  const { mutateAsync: createTenant, isPending: isCreating } = useMutation({
    mutationFn: (body: any) =>
      rootApiService.post(endpoints_tenant.createTenant, body),
    onSuccess: () => {
      notification.success({ message: "Tạo tenant thành công" });
      queryClient.invalidateQueries({
        queryKey: [endpoints_tenant.listTenant, params],
      });
    },
    onError: () => {
      notification.error({ message: "Tạo tenant thất bại" });
    },
  });

  const { mutateAsync: updateTenant, isPending: isUpdating } = useMutation({
    mutationFn: (body: any) =>
      rootApiService.post(endpoints_tenant.updateTenant, body),
    onSuccess: () => {
      notification.success({ message: "Cập nhật thành công" });
      queryClient.invalidateQueries({
        queryKey: [endpoints_tenant.listTenant, params],
      });
    },
    onError: () => {
      notification.error({ message: "Cập nhật thất bại" });
    },
  });

  const { mutateAsync: activeTenant, isPending: isActivating } = useMutation({
    mutationFn: (body: any) =>
      rootApiService.post(endpoints_tenant.activeTenant, body),
    onSuccess: () => {
      notification.success({ message: "Kích hoạt tenant thành công" });
      queryClient.invalidateQueries({
        queryKey: [endpoints_tenant.listTenant, params],
      });
    },
    onError: () => {
      toastService.error("Kích hoạt tenant thất bại");
    },
  });

  const { mutateAsync: inactiveTenant, isPending: isDeactivating } =
    useMutation({
      mutationFn: (body: any) =>
        rootApiService.post(endpoints_tenant.inactiveTenant, body),
      onSuccess: () => {
        notification.success({ message: "Ngưng kích hoạt tenant thành công" });
        queryClient.invalidateQueries({
          queryKey: [endpoints_tenant.listTenant, params],
        });
      },
      onError: () => {
        toastService.error("Ngưng kích hoạt tenant thất bại");
      },
    });
  //Lấy thông tin các tài khoản liên quan tenant
  const { mutateAsync: getInfoTenant, isPending: isGetInfo } = useMutation({
    mutationFn: (body: {
      tenantId: string;
      pageSize?: number;
      pageIndex?: number;
      createdDateFrom?: string;
      createdDateTo?: string;
    }) => rootApiService.post(endpoints_tenant.informationTenant, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_tenant.informationTenant, params],
      });
    },
    onError: () => {
      toastService.error("Lấy thông tin tài khoản thất bại");
    },
  });

  return {
    data: formatData,
    total,
    isLoading,
    refetch,
    createTenant,
    activeTenant,
    inactiveTenant,
    isCreating,
    isActivating,
    isDeactivating,
    getDetails,
    updateTenant,
    isUpdating,
    details,
    isDetailLoading,
    getInfoTenant,
    isGetInfo,
  };
};

export default useTenant;
