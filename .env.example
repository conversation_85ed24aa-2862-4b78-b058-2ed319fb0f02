PORT=4001
TZ=UTC
REQUEST_TIMEOUT=180000
#Swagger Config
SWAGGER_TITLE="APE LICENSE API DOCUMENTATION"
SWAGGER_DESCRIPTION="The APE LICENSE API DOCUMENTATION"
SWAGGER_VERSION="1.0"
# Primary Database
DB_PRIMARY_HOST=localhost
DB_PRIMARY_PORT=5432
DB_PRIMARY_USERNAME=postgres
DB_PRIMARY_PASSWORD=Abc12345
DB_PRIMARY_DATABASE=ape-license-dev
DB_PRIMARY_SYNCHRONIZE=true
DB_PRIMARY_SSL=true
DB_PRIMARY_SSL_REJECT_UNAUTHORIZED=true
# JWT HS256 config
JWT_SECRET="aab63b0c-a0ff-47e4-b1ea-3f9f4b73a5cd"
JWT_EXPIRY="10h"
JWT_REFRESH_TOKEN_SECRET="2737198c-f6a8-45d1-bff0-5f74c9fd499f"
JWT_REFRESH_TOKEN_EXPIRY="30d"
DISPLAY_TIMEZONE="Asia/Ho_Chi_Minh"
