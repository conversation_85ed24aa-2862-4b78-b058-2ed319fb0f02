import React from "react";
import {
  Typo<PERSON>,
  Space,
  Button,
  Tag,
  Avatar,
  Input,
  Popconfirm,
  Select,
} from "antd";
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  PlusOutlined,
  StopOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import BaseTable from "~/components/BaseTable";
import useAccount from "~/hooks/account/useAccount";
import {ListAccountDTO} from "~/dto/account.dto";
import BaseView from "~/components/BaseView";
import CreateForm from "./components/CreateForm";
import UpdateForm from "./components/UpdateForm";
import {ResetPasswordModal} from "./components/ResetPasswordModal";
import {notification} from "antd";
import SelectTenant from "~/components/ApiComponents/SelectTenant";
import {logoAssets} from "~/assets";

const {Text} = Typography;
const {Option} = Select;

export const Account = () => {
  const [openDrawerCreate, setOpenDrawerCreate] = React.useState(false);
  const [openDrawerUpdate, setOpenDrawerUpdate] = React.useState(false);
  const [selectedAccount, setSelectedAccount] = React.useState<any>(null);
  const [openDrawerResetPassword, setOpenDrawerResetPassword] =
    React.useState(false);

  // Use custom hook to get account list
  const [tenantCode, setTenantCode] = React.useState<string | string[]>([]);
  const [accountType, setAccountType] = React.useState<string | string[]>([]);
  const [searchValue, setSearchValue] = React.useState<string | string[]>([]);
  const [params, setParams] = React.useState<ListAccountDTO>({
    pageIndex: 1,
    pageSize: 20,
    data: [],
    total: 0,
  });
  const {data, isLoading, inactiveAccount, activeAccount, refetch} = useAccount(params);
  const [actionLoading, setActionLoading] = React.useState<
    Record<string, boolean>
  >({});
  const accounts = data?.pages?.[0]?.data || [];
  const total = data?.pages?.[0]?.total || 0;

  // Only columns: action (reset password, active/inactive)
  const columns = [
    {
      title: "STT",
      dataIndex: "stt",
      key: "stt",
      render: (text: string, record: any, index: number) => index + 1,
    },
    {
      title: 'Avatar',
      dataIndex: "avatar",
      key: "avatar",
      render: (text) => {
        if(!text) {
          return <Avatar src={logoAssets.logo} />
        }
        return <Avatar src={text} />
      }
    },
    {
      title: "Tên",
      dataIndex: "fullName",
      key: "fullName",
    },
    {
      title: "Tài khoản",
      dataIndex: "username",
      key: "username",
    },
    {
      title: "Tenant",
      dataIndex: "tenantName",
      key: "tenantName",
    },
    {
      title: "Loại tài khoản",
      dataIndex: "type",
      key: "type",
      render: (text: string) => {
        switch (text) {
          case "TENANT_MASTER":
            return <Tag color="blue">Quản trị viên</Tag>;
          case "TENANT_USER":
            return <Tag color="green">Người dùng</Tag>;
          case "CUSTOMER":
            return <Tag color="orange">Khách hàng</Tag>;
          default:
            return <Tag color="default">Khách</Tag>;
        }
      },
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (text: string) => new Date(text).toLocaleString("vi-VN"),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (status: string) =>
        status === "ACTIVE" ? (
          <Tag color="green">Hoạt động</Tag>
        ) : (
          <Tag color="red">Không hoạt động</Tag>
        ),
    },
    {
      key: "actions",
      title: "Hành động",
      render: (account: any) => (
        <Space>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={() => handleResetPassword(account)}
          />
          {account.status === "ACTIVE" ? (
            <Popconfirm
              title="Vô hiệu hóa tài khoản?"
              description={`Tài khoản: ${account.username}`}
              okText="Xác nhận"
              cancelText="Hủy"
              onConfirm={() => doInactive(account)}
              okButtonProps={{loading: !!actionLoading[account.id]}}
            >
              <Button type="text" icon={<StopOutlined />} danger />
            </Popconfirm>
          ) : (
            <Popconfirm
              title="Kích hoạt tài khoản?"
              description={`Tài khoản: ${account.username}`}
              okText="Xác nhận"
              cancelText="Hủy"
              onConfirm={() => doActive(account)}
              okButtonProps={{loading: !!actionLoading[account.id]}}
            >
              <Button type="dashed" icon={<CheckCircleOutlined />} />
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const handleResetPassword = (account: any) => {
    setSelectedAccount(account);
    setOpenDrawerResetPassword(true);
  };

  const doInactive = async (account: any) => {
    setActionLoading((prev) => ({...prev, [account.id]: true}));
    try {
      await inactiveAccount(account.id);
      notification.success({message: "Đã vô hiệu hóa tài khoản"});
      await refetch();
    } finally {
      setActionLoading((prev) => ({...prev, [account.id]: false}));
    }
  };

  const doActive = async (account: any) => {
    setActionLoading((prev) => ({...prev, [account.id]: true}));
    try {
      await activeAccount(account.id);
      notification.success({message: "Đã kích hoạt tài khoản"});
      await refetch();
    } finally {
      setActionLoading((prev) => ({...prev, [account.id]: false}));
    }
  };

  const handleFilter = () => {
    setParams((prev) => ({...prev, domain: tenantCode, type: accountType, searchValue}));
  };

  const handleOnChangeTenant = (value: string | string[]) => {
    setTenantCode(value);
  };

  const handleOnChangeType = (value: string) => {
    setAccountType(value);
  };

  const handleOnChangeSearchValue = (value: string) => {
    setSearchValue(value);
  };

  return (
    <BaseView>
      <Space
        style={{
          width: "100%",
          justifyContent: "space-between",
          marginBottom: 12,
        }}
      >
        {/* Search account */}
        <Space>
          <Input
            onChange={(e) => handleOnChangeSearchValue(e.target.value)}
            placeholder="Tìm kiếm tài khoản..."
            prefix={<SearchOutlined />}
            style={{width: 250}}
          />
          {/* Tìm theo Tenant */}
          <SelectTenant onChange={handleOnChangeTenant} />
          {/* Loại tài khoản */}
          <Select defaultValue="all" style={{width: 120}} onChange={handleOnChangeType}>
            <Option value="all">Tất cả</Option>
            <Option value="TENANT_MASTER">Quản trị viên</Option>
            <Option value="TENANT_USER">Người dùng</Option>
            <Option value="CUSTOMER">Khách hàng</Option>
          </Select>
          <Button icon={<FilterOutlined />} onClick={handleFilter}>
            Lọc
          </Button>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setOpenDrawerCreate(true)}
        >
          Thêm tài khoản
        </Button>
      </Space>
      <CreateForm
        openDrawer={openDrawerCreate}
        setOpenDrawer={setOpenDrawerCreate}
      />
      <UpdateForm
        openDrawer={openDrawerUpdate}
        setOpenDrawer={setOpenDrawerUpdate}
        data={selectedAccount}
      />
      {selectedAccount && (
        <ResetPasswordModal
          open={openDrawerResetPassword}
          onClose={() => setOpenDrawerResetPassword(false)}
          data={selectedAccount}
        />
      )}
      <div
        style={{
          overflowY: "auto",
          height: "calc(100vh - 200px)",
        }}
      >
        <BaseTable
          columns={columns}
          data={accounts}
          total={total}
          isLoading={isLoading}
        />
      </div>
    </BaseView>
  );
};
