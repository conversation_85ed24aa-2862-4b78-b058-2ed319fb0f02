# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-authenticator-admin-dev
  namespace: ape-authenticator-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-authenticator-admin-dev
  template:
    metadata:
      labels:
        app: ape-authenticator-admin-dev
    spec:
      containers:
        - name: ape-authenticator-admin-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-authenticator-admin-dev:latest
          ports:
            - containerPort: 80
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh
---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-authenticator-admin-dev
  namespace: ape-authenticator-dev
  labels:
    run: ape-authenticator-admin-dev
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-authenticator-admin-dev
