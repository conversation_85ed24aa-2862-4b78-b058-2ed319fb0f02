export interface ApplicationListRes {
    data: ApplicationItem[];
    total: number;
}

export interface ApplicationItem {
    id: string;
    createdDate: string;
    updatedDate: string;
    createdBy: any;
    updatedBy: any;
    name: string;
    code: string;
    description: string;
    version: string;
    tenants: Tenant[];
    status: string;
}

export interface Tenant {
    id: string;
    name: string;
    domain: string;
    status: string
}

export interface UpdateApplication {
    id: string;
    name: string;
    description: string;
    status: string;
}

export interface UseListApplicationParams {
  pageIndex: number;
  pageSize: number;
  searchValue?: string;
  createDateFrom?: string;
  createdDateTo?: string;
  status?: string;
}

export interface ICreateApplicationParams {
  name: string;
  description: string;
  version: string;
  status: string;
}