import { PageRequest } from "~/@ui/GridControl/models";

export interface UserAdminDTO {
  id?: string;
  username?: string;
  name?: string;
  fullName?: string;
  avatar?: string;
  isMasterTenant?: boolean;
  type?: string;
  status?: "ACTIVE" | "INACTIVE";
  createdDate?: string;
  updatedDate?: string;
}
export interface UpdateUserAdminDTO {
  id: string;
  email?: string;
  name?: string;
  status?: "ACTIVE" | "INACTIVE";
}
export interface ListUserAdminDTO extends PageRequest {
  data: UserAdminDTO[];
  total: number;
}

export interface ResetPasswordDTO {
  accountId: string;
  newPassword: string;
  confirmNewPassword: string;
}