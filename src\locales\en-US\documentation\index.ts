export const en_US_documentation = {
  'app.documentation.introduction.title': 'Introduction',
  'app.documentation.introduction.description': `
    react-antd-admin is an enterprise - level background management system template based on react and ant-design.
    Use the latest React Hooks API instead of the traditional class API,
    Typescript was also used to standardize code readability and maintainability, enhancing development efficiency,
    Use redux as the global state management library.
    This project allows you to quickly develop a new project template and remove some of the code according to your needs. 
    If you don't have a need to use templates,
    This project will also be a good resource for learning react and typescript.
    In addition, if you think this project is worth optimizing or modifying, 
    please feel free to ask, my contact information will be shown at the bottom of the article.
  `,
  'app.documentation.catalogue.title': 'Catalogue',
  'app.documentation.catalogue.description': 'Click the catalogue to quickly reach the specified content',
  'app.documentation.catalogue.list.layout': 'Layout',
  'app.documentation.catalogue.list.routes': 'Routes',
  'app.documentation.catalogue.list.request': 'HTTP Request',
  'app.documentation.catalogue.list.theme': 'Theme',
  'app.documentation.catalogue.list.typescript': 'Typescript',
  'app.documentation.catalogue.list.international': 'International',
};
