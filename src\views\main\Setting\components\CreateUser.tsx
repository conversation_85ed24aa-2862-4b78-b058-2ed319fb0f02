import React, {useState} from "react";
import {Button, Form, Input, Space, Drawer, Upload} from "antd";
import {CameraOutlined, CloseOutlined, SaveOutlined} from "@ant-design/icons";

export const CreateUser = ({openDrawer, setOpenDrawer}) => {
  const [form] = Form.useForm();
  
  const handleCreateApp = () => {
    form.submit()
  };
  const handleSubmit = () => {
    handleCreateApp()
  };
  const handleUpload = () => {};
  const handleClear = () => {};
  return (
    <Drawer
      title="Tạo tài khoản mới"
      width={800}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
      extra={
        <Space>
          <Button onClick={handleClear} icon={<CloseOutlined />}>
            Clear
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSubmit}>
            <PERSON><PERSON><PERSON>
          </Button>
        </Space>
      }
    >
      <Space
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center", // Center
          marginBottom: 16,
        }}
      >
        <Upload
          beforeUpload={() => false}
          accept="image/*"
          showUploadList={false}
          fileList={[]}
          onChange={handleUpload}
        >
          <Button type="primary" icon={<CameraOutlined />} size="large" />
        </Upload>
      </Space>
      <Form layout="vertical" form={form} onFinish={handleCreateApp}>
        <Form.Item
          name="username"
          label="Tài khoản"
          rules={[{required: true, message: "Vui lòng nhập tài khoản"}]}
        >
          <Input placeholder="Nhập tài khoản" />
        </Form.Item>
        <Form.Item
          name="password"
          label="Mật khẩu"
          rules={[{required: true, message: "Nhập mật khẩu"}]}
        >
          <Input.Password placeholder="Nhập mật khẩu" />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          label="Xác nhận mật khẩu"
          rules={[{required: true, message: "Xác nhận mật khẩu"}]}
        >
          <Input.Password placeholder="Xác nhận mật khẩu" />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
