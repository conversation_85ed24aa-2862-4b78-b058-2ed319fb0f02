import {useInfiniteQuery, useMutation, useQuery} from "@tanstack/react-query";
import {rootApiService} from "~/services/@common";
import {endpoints_application} from "~/services/endpoints";
import {
  ApplicationListRes,
  UseListApplicationParams,
  ICreateApplicationParams,
  UpdateApplication,
} from "~/dto/application.dto";
import {notification} from "antd";
import {useQueryClient} from "@tanstack/react-query";
import {useState} from "react";

const useApplication = (params?: UseListApplicationParams) => {
  const queryClient = useQueryClient();
  const {data, isLoading, refetch} = useInfiniteQuery<
    ApplicationListRes,
    Error
  >({
    queryKey: [
      endpoints_application.listApplication,
      {
        pageSize: params?.pageSize,
        pageIndex: params?.pageIndex,
      },
    ],
    queryFn: ({pageParam = 1}) => {
      return rootApiService.get<ApplicationListRes>(
        endpoints_application.listApplication, {
          ...params,
          pageSize: params?.pageSize,
          pageIndex: params?.pageIndex,
        }
      );
    },
    getNextPageParam: (lastPage, allPages) =>
      lastPage.data.length > 0 ? allPages.length + 1 : undefined,
    initialPageParam: 1,
  });

  // Create Application
  const {mutateAsync: createApplication, isPending: isCreating} = useMutation({
    mutationFn: (body: ICreateApplicationParams) =>
      rootApiService.post(endpoints_application.createApplication, body),
    onSuccess: (data) => {
      notification.success({
        message: "Tạo ứng dụng thành công",
      });
      queryClient.invalidateQueries({
        queryKey: [
          endpoints_application.listApplication,
          {
            pageSize: 10,
            pageIndex: 1,
          },
        ],
      });
    },
    onError: (e: any) => {
      notification.error({
        message: "Tạo ứng dụng thất bại",
      });
    },
  });

  // Update Application
  const {mutateAsync: updateApplication, isPending: isUpdating} = useMutation({
    mutationFn: (body: UpdateApplication) =>
      rootApiService.post(endpoints_application.updateApplication, body),
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: [
          endpoints_application.listApplication,
          {
            pageSize: 10,
            pageIndex: 1,
          },
        ],
      });
      notification.success({
        message: "Cập nhật ứng dụng thành công",
      });
    },
    onError: (e: any) => {
      notification.error({
        message: "Cập nhật ứng dụng thất bại",
      });
    },
  });

  const formatData = data?.pages.flatMap((page) => page.data) ?? [];
  const total = data?.pages[0]?.total ?? 0;

  return {
    data: formatData,
    total,
    isLoading,
    refetch,
    createApplication,
    updateApplication,
    isCreating,
    isUpdating,
  };
};

export default useApplication;
