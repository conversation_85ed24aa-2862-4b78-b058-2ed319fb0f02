import { Tabs } from "antd";
import { FC, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useLayoutConfig } from "~/stores/layoutConfig";

function pipePath(path: string) {
  const regexConfig = /(^\/+|\/+$)/gm;
  return path.replace(regexConfig, "");
}

type IMainTabsProps = {};

const MainTabs: FC<IMainTabsProps> = (props: IMainTabsProps) => {
  const {
    selectedKey,
    tabs,
    setActiveTab,
    flatLayoutResource,
    addTab,
    removeTab,
  } = useLayoutConfig();

  const { pathname, search } = useLocation();

  const onChange = (key: string) => {
    setActiveTab(key);
  };
  const onEdit = (targetKey: string, action: "add" | "remove") => {
    if (action === "remove") {
      removeTab(targetKey);
    }
  };

  useEffect(() => {
    const tab = flatLayoutResource.find(
      (v) => pipePath(v.rootPath) === pipePath(pathname)
    );

    if (tab) {
      addTab(tab, search);
    }
  }, [addTab, flatLayoutResource, pathname, search]);
  return (
    <div id="pageTabs" style={{ padding: "10px 10px 0px 10px" }}>
      <Tabs
        tabBarStyle={{ margin: 0 }}
        onChange={onChange}
        activeKey={selectedKey}
        type="editable-card"
        hideAdd
        onEdit={onEdit}
        items={tabs.map((tab) => {
          return {
            key: tab.key,
            closable: tab.closable,
            label: tab.title,
          };
        })}
      />
    </div>
  );
};
export default MainTabs;
