import {ListAccountDTO, ResetPasswordDTO} from "~/dto/account.dto";
import {rootApiService} from "~/services/@common";
import {endpoints_account} from "~/services/endpoints";
import {useInfiniteQuery} from "@tanstack/react-query";

const useAccount = (params?: ListAccountDTO) => {
  const {data, isLoading, refetch} = useInfiniteQuery<ListAccountDTO>({
    queryKey: [endpoints_account.listAccounts, params],
    queryFn: () => {
      return rootApiService.get<ListAccountDTO>(endpoints_account.listAccounts, {
        ...params,
        pageSize: params?.pageSize || 10,
        pageIndex: params?.pageIndex || 1,
      });
    },
    getNextPageParam: (lastPage, allPages) =>
      lastPage.data.length > 0 ? allPages.length + 1 : undefined,
    initialPageParam: 1,
  });
  // Reset password changePassword

  const changePassword = (body: ResetPasswordDTO) => {
    return rootApiService.post(`${endpoints_account.changePassword}`, {
      ...body
    });
  };

  const createAccount = (body: any) => {
    return rootApiService.post(`${endpoints_account.createAccount}`, {
      ...body
    });
  };

  // Inactive account

  const inactiveAccount = (id: string) => {
    return rootApiService.post(`${endpoints_account.inactiveAccount}`, {id});
  };

  // Active account

  const activeAccount = (id: string) => {
    return rootApiService.post(`${endpoints_account.activeAccount}`, {id});
  };
  return {
    data,
    isLoading,
    refetch,
    changePassword,
    inactiveAccount,
    activeAccount,
    createAccount
  };
};

export default useAccount;
