export const zhCN_documentation = {
  'app.documentation.introduction.title': '介绍',
  'app.documentation.introduction.description': `
    react-antd-admin是一个基于react和ant-design开发的企业级中后台管理系统模板。
    使用了最新的React Hooks API代替了传统的class API，
    并且使用了typescript来规范代码的可读性和维护性，增强开发效率，
    使用redux作为全局的状态管理库。
    此项目可以你的新项目模板快速开发，根据自己的需求删除掉部分代码。如果你没有使用模板的需求，
    此项目也会是一个学习react和typescript的好的资料。
    此外，如果你觉得此项目有值得优化或修改的地方，也欢迎提出，我的联系方式将会显示在文章底部。
  `,
  'app.documentation.catalogue.title': '目录',
  'app.documentation.catalogue.description': '点击目录到达指定内容',
  'app.documentation.catalogue.list.layout': '布局',
  'app.documentation.catalogue.list.routes': '路由',
  'app.documentation.catalogue.list.request': '网络请求',
  'app.documentation.catalogue.list.theme': '主题',
  'app.documentation.catalogue.list.typescript': 'Typescript',
  'app.documentation.catalogue.list.international': '国际化',
};
