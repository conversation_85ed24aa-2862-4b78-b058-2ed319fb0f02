export const enUS_guide = {
  'app.guide.guideIntro': `The guide page is useful for
                           some people who entered the 
                           project for the first time. 
                           You can briefly introduce 
                           the features of the project. 
                           Demo is based on`,
  'app.guide.showGuide': 'Show Guide',
  'app.guide.driverjs.closeBtnText': 'Close',
  'app.guide.driverjs.prevBtnText': 'Previous',
  'app.guide.driverjs.nextBtnText': 'Next',
  'app.guide.driverjs.doneBtnText': 'Done',
  'app.guide.driverStep.sidebarTrigger.title': 'Sidebar Trigger',
  'app.guide.driverStep.sidebarTrigger.description': 'Open and close the Sidebar',
  'app.guide.driverStep.notices.title': 'Notices',
  'app.guide.driverStep.notices.description': 'All notification messages were be displayed here',
  'app.guide.driverStep.switchLanguages.title': 'Switch Languages',
  'app.guide.driverStep.switchLanguages.description': 'You can click here to switch languages',
  'app.guide.driverStep.pageTabs.title': 'Page Tabs',
  'app.guide.driverStep.pageTabs.description': 'The history of the page you visited will be displayed here',
  'app.guide.driverStep.pageTabsActions.title': 'Page Tabs Actions',
  'app.guide.driverStep.pageTabsActions.description': 'Click here to do some quick operations to the Page Tabs',
  'app.guide.driverStep.switchTheme.title': 'Switch Theme',
  'app.guide.driverStep.switchTheme.description': 'Click here to switch system theme color',
};
