import { useQuery } from "@tanstack/react-query";
import { Stats, TopTenants } from "~/dto/layout/dashboard.dto";
import { rootApiService, toastService } from "~/services/@common";
import { endpoints_dashboard } from "~/services/endpoints";

const useDashboard = () => {
  // Stats (GET, no pagination)
  const {
    data: statsData,
    isLoading: isLoadingStats,
    isError: isStatsError,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<Stats, Error>({
    queryKey: [endpoints_dashboard.stats],
    queryFn: () => rootApiService.get(endpoints_dashboard.stats),
  });

  // Top tenants (GET, no pagination)
  const {
    data: topTenantsData,
    isLoading: isLoadingTopTenants,
    isError: isTopTenantsError,
    error: topTenantsError,
    refetch: refetchTopTenants,
  } = useQuery<TopTenants, Error>({
    queryKey: [endpoints_dashboard.topTenants],
    queryFn: () => rootApiService.get(endpoints_dashboard.topTenants),
  });

  return {
    // data trả nguyên bản từ API (không format)
    statsData,
    topTenantsData,

    // loading & error state
    isLoadingStats,
    isLoadingTopTenants,
    isStatsError,
    isTopTenantsError,
    statsError,
    topTenantsError,

    // tiện refetch thủ công
    refetchStats,
    refetchTopTenants,
  };
};

export default useDashboard;
