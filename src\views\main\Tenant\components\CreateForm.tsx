import React, {useEffect, useState} from "react";
import {
  Button,
  Form,
  Input,
  Select,
  Space,
  Drawer,
  Divider,
  Typography,
} from "antd";
import {CloseOutlined, SaveOutlined, PlusOutlined, UserOutlined} from "@ant-design/icons";
import useTenant from "~/hooks/tenant/useTenant";
import SelectApplication from "~/components/ApiComponents/SelectApplication";
import {CreateTenantAppReq} from "~/dto/tenant.dto";
import {notification} from "antd";
import {Row, Col} from "antd";

const {Option} = Select;
const {Text} = Typography;

const CreateForm = ({openDrawer, setOpenDrawer}) => {
  const {createTenant, isCreating} = useTenant({
    pageIndex: 1,
    pageSize: 10,
  });
  const [applications, setApplications] = useState<string[]>([]);
  const [appArray, setAppArray] = useState<CreateTenantAppReq[]>([]);
  const [domain, setDomain] = useState<string>("@");
  const [form] = Form.useForm();

  const handleCreatePartner = (values: any) => {
    values.applications = appArray;
    // check password
    if (values.rootPassword !== values.rootPasswordAgain) {
      notification.error({
        message: "Mật khẩu không khớp",
      });
      return;
    }
    delete values.rootPasswordAgain;
    createTenant(values).finally(() => setOpenDrawer(false));
  };

  const handleSubmit = () => {
    form.submit();
  };

  const handleClear = () => {
    form.resetFields();
    setApplications([]);
    setAppArray([]);
  };

  const handleSelectApplication = (value: any) => {
    setApplications(value);
  };

  const updateRedirectUri = (code: string, index: number, value: string) => {
    setAppArray((prev) =>
      prev.map((app) =>
        app.code === code
          ? {
              ...app,
              redirectUris: app.redirectUris.map((uri, idx) =>
                idx === index ? value : uri
              ),
            }
          : app
      )
    );
  };

  const addRedirectUri = (code: string) => {
    setAppArray((prev) =>
      prev.map((app) =>
        app.code === code
          ? {...app, redirectUris: [...app.redirectUris, ""]}
          : app
      )
    );
  };

  const deleteRedirectUri = (code: string, index: number) => {
    setAppArray((prev) =>
      prev.map((app) =>
        app.code === code
          ? {
              ...app,
              redirectUris: app.redirectUris.filter((_, idx) => idx !== index),
            }
          : app
      )
    );
  };

  useEffect(() => {
    if (Array.isArray(applications)) {
      setAppArray(
        applications.map((code) => ({
          code,
          redirectUris: [],
        }))
      );
    }
    return () => {
      setAppArray([]);
      setDomain("@");
    };
  }, [applications]);
  
  return (
    <Drawer
      title="Tạo đối tác mới"
      width={1000}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
      extra={
        <Space>
          <Button onClick={handleClear} icon={<CloseOutlined />}>
            Clear
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            htmlType="submit"
            onClick={handleSubmit}
            loading={isCreating}
          >
            Lưu
          </Button>
        </Space>
      }
    >
      <Form layout="vertical" form={form} onFinish={handleCreatePartner}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="domain"
              label="Mã đối tác"
              rules={[{required: true, message: "Nhập mã đối tác"}]}
            >
              <Input placeholder="Nhập mã đối tác" onChange={(e) => setDomain(`@${e.target.value}`)}  />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="name"
              label="Tên đối tác"
              rules={[{required: true, message: "Vui lòng nhập tên đối tác"}]}
            >
              <Input placeholder="Nhập tên đối tác" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="rootUsername"
              label="Tài khoản"
              rules={[{required: true, message: "Nhập tên đăng nhập"}]}
            >
              <Input placeholder="Nhập tên đăng nhập" addonAfter={domain}/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="rootFullName"
              label="Tên tài khoảng"
              rules={[{required: true, message: "Vui lòng nhập tên đầy đủ"}]}
            >
              <Input placeholder="Nhập tên đầy đủ" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="rootPassword"
              label="Mật khẩu"
              rules={[{required: true, message: "Nhập mật khẩu"}]}
            >
              <Input.Password placeholder="Nhập mật khẩu" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="rootPasswordAgain"
              label="Nhập lại mật khẩu"
              rules={[{required: true, message: "Nhập lại mật khẩu"}]}
            >
              <Input.Password placeholder="Nhập lại mật khẩu" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="tenantDomain"
              label="Tên miền"
              rules={[{required: true, message: "Nhập tên miền"}]}
            >
              <Input placeholder="Nhập địa chỉ website" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="status"
              label="Trạng thái"
              rules={[{required: true, message: "Chọn trạng thái"}]}
            >
              <Select placeholder="Chọn trạng thái" defaultValue={"ACTIVE"}>
                <Option value="ACTIVE">Hoạt động</Option>
                <Option value="INACTIVE">Không hoạt động</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item name="taxCode" label="Mã số thuế">
              <Input placeholder="Nhập mã số thuế" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="phone" label="Số điện thoại">
              <Input placeholder="Nhập số điện thoại" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item name="email" label="Email">
              <Input placeholder="Nhập email" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="website" label="Website">
              <Input placeholder="Nhập địa chỉ website" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Divider />
      <Space
        style={{
          width: "100%",
          display: "flex",
          alignItems: "flex-start",
          justifyContent: "flex-start",
          flexDirection: "column",
          marginBottom: 16,
        }}
      >
        <Space>
          <Text>Cấu hình ứng dụng cho Tenant:</Text>
          <SelectApplication
            onChange={handleSelectApplication}
            value={applications}
          />
        </Space>

        <Space
          style={{width: "100%", display: "flex", flexDirection: "column"}}
        >
          {appArray.map((app) => (
            <div
              key={app.code}
              style={{
                marginBottom: 24,
                borderBottom: "1px solid #eee",
                paddingBottom: 16,
              }}
            >
              <h4>Ứng dụng: {app.code}</h4>
              {app.redirectUris.map((uri, index) => (
                <Space
                  key={`${app.code}-uri-${index}`}
                  style={{marginBottom: 8, marginLeft: 8}}
                >
                  <Text>Redirect URI {index + 1}:</Text>
                  <Input
                    style={{width: "400px"}}
                    value={uri}
                    onChange={(e) =>
                      updateRedirectUri(app.code, index, e.target.value)
                    }
                    placeholder="https://example.com/callback"
                  />
                  {/* Delete */}
                  <Button
                    danger
                    icon={<CloseOutlined />}
                    onClick={() => deleteRedirectUri(app.code, index)}
                  ></Button>
                </Space>
              ))}
              <Button
                style={{marginLeft: 8}}
                type="dashed"
                icon={<PlusOutlined />}
                onClick={() => addRedirectUri(app.code)}
              >
                Redirect URI
              </Button>
            </div>
          ))}
        </Space>
      </Space>
    </Drawer>
  );
};

export default CreateForm;
