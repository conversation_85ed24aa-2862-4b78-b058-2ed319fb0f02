import { rootApiService } from "./@common";
import { LoginReq, UserSessionDto } from "~/dto/auth.dto";
import { ApiException } from "~/@core/dto";
import { KeyLocalStore } from "~/common/constants";

const Endpoint = {
  login: "/api/admin/auth/login".trim(),
} as const;

export class AuthService {
  async login(body: LoginReq): Promise<UserSessionDto> {
    try {
      const userSession = await rootApiService.post<UserSessionDto>(
        Endpoint.login,
        body
      );

      return userSession;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }

      throw new ApiException("Đăng nhập thất bại", 400);
    }
  }

  isAuthenticated(): boolean {
    const accessToken = localStorage.getItem(KeyLocalStore.accessToken);
    return !!accessToken;
  }

  getAccessToken(): string | null {
    return localStorage.getItem(KeyLocalStore.accessToken);
  }
}

export const authService = new AuthService();
