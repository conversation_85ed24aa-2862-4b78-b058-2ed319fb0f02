import React, { useEffect, useMemo, useState } from "react";
import { Select, Spin } from "antd";
import useApplication from "~/hooks/application/useApplication";

const SelectApplication = ({ onChange, value = [] }) => {
  const { data = [], isLoading } = useApplication({
    pageIndex: 1,
    pageSize: 100, // load nhiều hơn để đảm bảo value có trong options
  });

  // Convert data to options format
  const options = useMemo(() => {
    return data.map((item) => ({
      label: item.name,
      value: item.code,
    }));
  }, [data]);

  const handleOnChange = (val) => {
    onChange(val);
  };

  return (
    <Select
      mode="multiple"
      showSearch
      placeholder="Chọn ứng dụng"
      style={{ width: "400px" }}
      loading={isLoading}
      options={options}
      value={value}
      onChange={handleOnChange}
      allowClear
      notFoundContent={isLoading ? <Spin size="small" /> : "Không có dữ liệu"}
    />
  );
};

export default SelectApplication;
