import { useMutation } from "@tanstack/react-query";
import { rootApiService, toastService } from "~/services/@common";
import { endpoints_upload } from "~/services/endpoints";

const useUploadMutiple = () => {
  const { isError, data, error, mutateAsync, mutate, isPending } = useMutation({
    mutationFn: (variables: FormData) =>
      rootApiService.post(endpoints_upload.uploadMutiple, variables),
    onSuccess: (data) => {
      toastService.success("Upload thành công");
    },
    onError: (e: any) => {
      toastService.error("Upload thất bại");
    },
  });

  return {
    isError,
    data: data?.data,
    error,
    mutate,
    mutateAsync,
    isPending,
  };
};

export default useUploadMutiple;
