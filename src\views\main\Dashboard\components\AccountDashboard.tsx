import React from 'react';
import { Card, Col, Row, Statistic, Tag } from 'antd';
import {
  Bar,
  Pie,
  Line
} from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import dayjs from 'dayjs';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  TimeScale
);

export const AccountDashboard: React.FC = () => {
  // Gi<PERSON> lập dữ liệu thống kê
  const newUsersThisWeek = 28;
  const inactiveUsers = 5;
  const lockedUsers = 3;
  const avgActiveTimeMins = 42;

  const roleDistribution = {
    labels: ['Nhân viên', '<PERSON><PERSON>h nghiệp'],
    datasets: [
      {
        label: 'Tỷ lệ người dùng theo vai trò',
        data: [50, 5],
        backgroundColor: [ '#52c41a', '#faad14'],
      },
    ],
  };

  const userGrowthData = {
    labels: Array.from({ length: 7 }, (_, i) =>
      dayjs().subtract(6 - i, 'day').format('DD/MM')
    ),
    datasets: [
      {
        label: 'Người dùng mới',
        data: [2, 3, 4, 5, 4, 6, 4],
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        fill: false,
        tension: 0.4,
      },
    ],
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic title="🧍 Người dùng mới tuần này" value={newUsersThisWeek} suffix="người" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="🔐 Người dùng doanh nghiệp" value={inactiveUsers} suffix="người" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="⚠️ Người dùng là nhân viên" value={lockedUsers} suffix="người" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="🧍 Tổng người dùng trên hệ thống" value={avgActiveTimeMins} suffix="người" />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={12}>
          <Card title="📊 Vai trò người dùng">
            <div style={{ height: 300, width: 300 }}>
              <Pie data={roleDistribution} />
            </div>
            
          </Card>
        </Col>
        <Col span={12}>
          <Card title="📈 Tăng trưởng người dùng trong tuần">
            <Line data={userGrowthData} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
