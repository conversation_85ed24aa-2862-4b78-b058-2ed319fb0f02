import React from 'react';
import { Switch } from 'antd';

interface BaseSwitchProps {
  status: boolean;
  onChange?: (checked: boolean) => void;
  checkedChildren?: React.ReactNode;
  unCheckedChildren?: React.ReactNode;
  disabled?: boolean;
}

const BaseSwitch: React.FC<BaseSwitchProps> = ({
  status,
  onChange,
  checkedChildren,
  unCheckedChildren,
  disabled = false,
}) => {
  return (
    <Switch
      checked={status}
      onChange={onChange}
      checkedChildren={checkedChildren}
      unCheckedChildren={unCheckedChildren}
      disabled={disabled}
    />
  );
};

export default BaseSwitch;
