import React from "react";
import {
  Button,
  Form,
  Input,
  Layout,
  Spin,
  Typography,
  ConfigProvider,
  theme,
  Card,
  Image,
} from "antd";
import {useAuthStore} from "~/stores/authStore";
import {COLORS} from "~/common/constants";
import BaseCheckbox from "~/components/BaseCheckbox";
import {Content} from "antd/es/layout/layout";
import {logoAssets} from "~/assets";

const {Title} = Typography;

// TypeScript interfaces
interface LoginFormValues {
  username: string;
  password: string;
  remember?: boolean;
}

// Light theme config cố định cho login
const loginThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: COLORS.PRIMARY_RGB,
    colorBgContainer: COLORS.WHITE,
    colorBgBase: COLORS.WHITE,
    colorText: COLORS.BLACK,
    colorTextBase: COLORS.BLACK,
  },
  components: {
    Layout: {
      colorBgHeader: COLORS.WHITE,
      colorBgBody: COLORS.WHITE,
    },
    Input: {
      colorBgContainer: COLORS.WHITE,
      colorText: COLORS.BLACK,
    },
    Button: {
      colorPrimary: COLORS.PRIMARY_2,
    },
    Form: {
      labelColor: COLORS.BLACK,
    },
  },
};

const LoginView: React.FC = () => {
  const {login, isLoading} = useAuthStore();

  const handleFinish = (values: LoginFormValues) => {
    login({
      username: values.username,
      password: values.password,
    });
  };

  return (
    <Layout
      style={{
        width: "100vw",
        minHeight: "100vh",
      }}
    >
      <Content
        style={{
          width: "100vw",
          minHeight: "100vh",
          padding: "50px 0",
          backgroundColor: COLORS.WHITE,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          background:
            "linear-gradient(90deg, rgba(2, 0, 36, 1) 0%, rgba(9, 9, 121, 1) 35%, rgba(0, 212, 255, 1) 100%)",
        }}
      >
        {/* Logo */}

        <Card
          style={{width: 400, height: "auto"}}
          title={<Title level={4}>Chào mừng trở lại</Title>}
          extra={<Image src={logoAssets.logo} width={60} style={{padding: 8}} preview={false} />}
        >
          <Form<LoginFormValues>
            name="loginForm"
            layout="vertical"
            initialValues={{remember: true}}
            onFinish={handleFinish}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              label="Tài khoản"
              name="username"
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập tài khoản của bạn",
                },
                {
                  min: 3,
                  message: "Tài khoản phải có ít nhất 3 ký tự",
                },
              ]}
            >
              <Input placeholder="Nhập tài khoản của bạn" />
            </Form.Item>

            <Form.Item
              label="Mật khẩu"
              name="password"
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập mật khẩu của bạn",
                },
                {
                  min: 6,
                  message: "Mật khẩu phải có ít nhất 6 ký tự",
                },
              ]}
            >
              <Input.Password placeholder="Nhập mật khẩu của bạn" />
            </Form.Item>

            {/* <Form.Item name="remember" valuePropName="checked">
              <BaseCheckbox
                label="Ghi nhớ đăng nhập"
                checked={true}
                onChange={() => {}}
                sizeCheckbox="large"
                sizeText="large"
              />
            </Form.Item> */}

            <Form.Item style={{textAlign: "center"}}>
              <Button type="primary" htmlType="submit" loading={isLoading}>
                Đăng nhập
              </Button>
            </Form.Item>
          </Form>
          {/* Powered by Ape Tech Solutions */}
          <div style={{marginTop: 16, textAlign: "center"}}>
            <Title type="secondary">Powered by Ape Tech Solutions</Title>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default LoginView;
