

const getKeyEnumByValue = <T = any>(targetEnum: T, valueFind: any) => {
    return Object.keys(targetEnum)[Object.values(targetEnum).indexOf(valueFind)] || "";
}

const cleanObject = <T extends Record<string, any>>(obj: T): Partial<T> => {
    return Object.fromEntries(
        Object.entries(obj).filter(([_, v]) => v !== null && v !== undefined),
    ) as Partial<T>;
};


export {
    getKeyEnumByValue,
    cleanObject
}