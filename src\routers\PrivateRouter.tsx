import React, { FC, useEffect, useState } from "react";
import { Outlet, useLocation } from "react-router";
interface IProps {}
const PrivateRouter: FC<IProps> = (props: IProps) => {
  const [checkRole, setCheckRole] = useState(false);
  const check = async () => {
    await new Promise((res) => {
      setTimeout(() => {
        res("");
      }, 2000);
    });
    setCheckRole(true);
  };
  useEffect(() => {
    check();
  }, [checkRole]);
  if (checkRole) {
    return <Outlet />;
  }
  return null;
};

export default PrivateRouter;
