import {ResetPasswordDTO, ListUserAdminDTO} from "~/dto/userAdmin.dto";
import {rootApiService} from "~/services/@common";
import {endpoints_user_admin} from "~/services/endpoints";
import {useInfiniteQuery} from "@tanstack/react-query";

const useUserAdmin = (params?: ListUserAdminDTO) => {
  const {data, isLoading, refetch} = useInfiniteQuery<ListUserAdminDTO>({
    queryKey: [endpoints_user_admin.listAccounts, params],
    queryFn: () => {
      return rootApiService.get<ListUserAdminDTO>(
        endpoints_user_admin.listAccounts,
        {
          ...params,
          pageSize: params?.pageSize || 10,
          pageIndex: params?.pageIndex || 1,
        }
      );
    },
    getNextPageParam: (lastPage, allPages) =>
      lastPage.data.length > 0 ? allPages.length + 1 : undefined,
    initialPageParam: 1,
  });

  const changePassword = (body: ResetPasswordDTO) => {
    return rootApiService.post(`${endpoints_user_admin.changePassword}`, {
      ...body,
    });
  };

  const createAccount = (body: any) => {
    return rootApiService.post(`${endpoints_user_admin.createAccount}`, {
      ...body,
    });
  };

  const inactiveAccount = (id: string) => {
    return rootApiService.post(`${endpoints_user_admin.inactiveAccount}`, {id});
  };

  const activeAccount = (id: string) => {
    return rootApiService.post(`${endpoints_user_admin.activeAccount}`, {id});
  };

  return {
    data,
    isLoading,
    refetch,
    changePassword,
    inactiveAccount,
    activeAccount,
    createAccount,
  };
};

export default useUserAdmin;
