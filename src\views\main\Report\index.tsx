import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Typo<PERSON>,
  Space,
  Button,
  Divider,
  Progress,
} from 'antd';
import {
  ArrowUpOutlined,
  CalendarOutlined,
  DownloadOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

export const ReportView = () => {
  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Space>
          <Button icon={<CalendarOutlined />}>Chọn thời gian</Button>
          <Button icon={<DownloadOutlined />} type="primary">
            Xuất báo cáo
          </Button>
        </Space>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card>
            <Text strong>Tăng trưởng Tenant</Text>
            <br />
            <Text type="secondary">So với tháng trước</Text>
            <div style={{ marginTop: 16 }}>
              <Space>
                <ArrowUpOutlined style={{ color: 'green' }} />
                <Text strong style={{ color: 'green', fontSize: 18 }}>
                  +25%
                </Text>
              </Space>
              <br />
              <Text type="secondary">2 tenant mới</Text>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card>
            <Text strong>Tỷ lệ sử dụng</Text>
            <br />
            <Text type="secondary">Người dùng hoạt động</Text>
            <div style={{ marginTop: 16 }}>
              <Text strong style={{ fontSize: 20, color: '#1677ff' }}>
                78%
              </Text>
              <br />
              <Text type="secondary">114/146 người dùng</Text>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card>
            <Text strong>Doanh thu trung bình</Text>
            <br />
            <Text type="secondary">Mỗi tenant/tháng</Text>
            <div style={{ marginTop: 16 }}>
              <Text strong style={{ fontSize: 20, color: '#9B5DE5' }}>
                $2,667
              </Text>
              <br />
              <Text type="secondary">+15% tháng trước</Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={12}>
          <Card title="Top Tenant theo doanh thu">
            <div>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>
                    <b>1</b>. Enterprise DEF <br />
                    <Text type="secondary">Enterprise</Text>
                  </Text>
                  <Text>
                    $5000 <br />
                    <Text type="secondary">89 users</Text>
                  </Text>
                </div>
                <Divider style={{ margin: '12px 0' }} />
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>
                    <b>2</b>. Công ty ABC <br />
                    <Text type="secondary">Enterprise</Text>
                  </Text>
                  <Text>
                    $2500 <br />
                    <Text type="secondary">45 users</Text>
                  </Text>
                </div>
                <Divider style={{ margin: '12px 0' }} />
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>
                    <b>3</b>. Startup XYZ <br />
                    <Text type="secondary">Pro</Text>
                  </Text>
                  <Text>
                    $500 <br />
                    <Text type="secondary">12 users</Text>
                  </Text>
                </div>
              </Space>
            </div>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="Phân bổ theo gói dịch vụ">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>
                  <span style={{ color: '#9B5DE5' }}>■</span> Enterprise
                </Text>
                <Text>67% (2 tenant)</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>
                  <span style={{ color: '#4CC9F0' }}>■</span> Pro
                </Text>
                <Text>33% (1 tenant)</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>
                  <span style={{ color: '#A0A0A0' }}>■</span> Basic
                </Text>
                <Text>0% (0 tenant)</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      <Card
        title="Phân tích sử dụng ứng dụng"
        style={{ marginTop: 24 }}
        extra={<Text type="secondary">Số lượng tenant sử dụng mỗi ứng dụng</Text>}
      >
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {[
            { name: 'CRM System', desc: 'Quản lý khách hàng', tenants: 2, version: 'v2.1.0' },
            { name: 'HR Management', desc: 'Quản lý nhân sự', tenants: 1, version: 'v1.5.2' },
            { name: 'Analytics Dashboard', desc: 'Báo cáo và phân tích', tenants: 3, version: 'v3.0.1' },
          ].map((app, index) => (
            <div
              key={index}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                background: '#FAFAFA',
                padding: 12,
                borderRadius: 8,
              }}
            >
              <div>
                <Text strong>{app.name}</Text>
                <br />
                <Text type="secondary">{app.desc}</Text>
              </div>
              <div>
                <Text>
                  {app.tenants} tenant
                  <br />
                  <Text type="secondary">{app.version}</Text>
                </Text>
              </div>
            </div>
          ))}
        </Space>
      </Card>
    </div>
  );
};
