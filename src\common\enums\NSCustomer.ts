export namespace NSCustomer {
  export enum ECustomerType {
    ADMIN = "ADMIN",
    CUSTOMER = "CUSTOMER",
  }
  export enum ECustomerStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
  }
}

export const ECustomer = {
  ECustomerStatus: {
    ACTIVE: {
      label: "Active",
      value: NSCustomer.ECustomerStatus.ACTIVE,
      name: "Hoạt động",
      color: "green",
    },
    INACTIVE: {
      label: "Inactive",
      value: NSCustomer.ECustomerStatus.INACTIVE,
      name: "Không hoạt động",
      color: "red",
    },
  },
};
