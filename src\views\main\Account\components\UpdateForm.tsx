import React, {useEffect} from "react";
import {Button, Form, Input, Select, Space, Drawer, Tag} from "antd";
import {CloseOutlined, SaveOutlined} from "@ant-design/icons";

const {Option} = Select;

const UpdateForm = ({openDrawer, setOpenDrawer, data}) => {
  const [form] = Form.useForm();

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    console.log("Updated values:", values);
    // Gọi API cập nhật tại đây nếu cần
  };

  const handleClear = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);
    }
  }, [data, form]);

  return (
    <Drawer
      title="Cập nhật tài khoản"
      width={800}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
      extra={
        <Space>
          <Button onClick={handleClear} icon={<CloseOutlined />}>
            Clear
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSubmit}>
            Lưu
          </Button>
        </Space>
      }
    >
      <Form layout="vertical" form={form} onFinish={handleFinish}>
        <Form.Item name="tenant" label="Tenant">
          <Select placeholder="Chọn tenant" disabled>
            <Option value="tenant1">Tenant 1</Option>
            <Option value="tenant2">Tenant 2</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="name"
          label="Tên tài khoản"
          rules={[{required: true, message: "Nhập tên tài khoản"}]}
        >
          <Input placeholder="Nhập tên tài khoản" />
        </Form.Item>
        <Form.Item
          name="email"
          label="Email"
          rules={[{required: true, message: "Nhập email"}]}
        >
          <Input placeholder="Nhập email" />
        </Form.Item>
        <Form.Item
          name="username"
          label="Tên đăng nhập"
          rules={[{required: true, message: "Nhập tên đăng nhập"}]}
        >
          <Input placeholder="Nhập tên đăng nhập" />
        </Form.Item>
        <Form.Item
          name="role"
          label="Vai trò"
          rules={[{required: true, message: "Chọn vai trò"}]}
        >
          <Select placeholder="Chọn vai trò">
            <Option value="admin">Admin</Option>
            <Option value="user">User</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="status"
          label="Trạng thái"
          rules={[{required: true, message: "Chọn trạng thái"}]}
        >
          <Select placeholder="Chọn trạng thái">
            <Option value="active">Hoạt động</Option>
            <Option value="inactive">Không hoạt động</Option>
          </Select>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default UpdateForm;
