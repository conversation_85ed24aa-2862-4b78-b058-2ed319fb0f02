import { PageRequest } from "~/@ui/GridControl/models";

export interface AccountDTO {
  id?: string;
  username?: string;
  name?: string;
  fullName?: string;
  avatar?: string;
  isMasterTenant?: boolean;
  type?: string;
  status?: "ACTIVE" | "INACTIVE";
  createdDate?: string;
  updatedDate?: string;
}
export interface UpdateAccountDTO {
  id: string;
  email?: string;
  name?: string;
  status?: "ACTIVE" | "INACTIVE";
}
export interface ListAccountDTO extends PageRequest {
  data: AccountDTO[];
  total: number;
}

export interface ResetPasswordDTO {
  accountId: string;
  newPassword: string;
  confirmNewPassword: string;
}