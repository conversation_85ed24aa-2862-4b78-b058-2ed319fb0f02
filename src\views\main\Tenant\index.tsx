import React, {useState, useEffect} from "react";
import {Tag, Input, Button, Space, Typography, DatePicker, Select} from "antd";
import {
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  EditOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import CreateForm from "./components/CreateForm";
import DetailView from "./components/DetailView";
import EditForm from "./components/EditForm";
import BaseTable from "~/components/BaseTable";
import BaseView from "~/components/BaseView";
import useTenant from "~/hooks/tenant/useTenant";
import {formatDateCustom} from "~/common/helper/helper";
import dayjs from "dayjs";
import {cleanObject} from "~/common/utils/common.utils";

const {RangePicker} = DatePicker;
const {Option} = Select;
const {Text} = Typography;

interface Tenant {
  id: string;
  domain: string;
  name: string;
  taxCode: string;
  phone: string;
  email: string;
  website: string;
  createdAt: string;
  status: "ACTIVE" | "INACTIVE";
  applications: string[];
  rootFullName: string;
  rootUsername: string;
}


export const TenantView: React.FC = () => {
  // UI State
  const [searchInput, setSearchInput] = useState<string>("");
  const [status, setStatus] = useState<string | null>();
  const [createdDateFrom, setCreatedDateFrom] = useState<string | undefined>();
  const [createdDateTo, setCreatedDateTo] = useState<string | undefined>();

  // Filter State (dùng thật để query)
  const [filterSearch, setFilterSearch] = useState<string | undefined>();
  const [filterStatus, setFilterStatus] = useState<string | undefined>();
  const [filterCreatedDateFrom, setFilterCreatedDateFrom] = useState<string | undefined>();
  const [filterCreatedDateTo, setFilterCreatedDateTo] = useState<string | undefined>();

  // Pagination State
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const tenantFilterRwa = {
    pageIndex,
    pageSize,
    searchValue: filterSearch,
    status: filterStatus,
    createdDateFrom: filterCreatedDateFrom
      ? dayjs(filterCreatedDateFrom).startOf("day").toISOString()
      : null,
    createdDateTo: filterCreatedDateTo
      ? dayjs(filterCreatedDateTo).endOf("day").toISOString()
      : null,
  };

  const tenantFilter = cleanObject(tenantFilterRwa);

  const {data, total, isLoading, refetch} = useTenant(tenantFilter);

  const [openDrawerCreate, setOpenDrawerCreate] = useState(false);
  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [openDrawerDetail, setOpenDrawerDetail] = useState(false);
  const [selectTenant, setSelectTenant] = useState<Tenant | null>(null);

  const handleOpenCreate = () => {
    setOpenDrawerCreate(true);
  };

  const handleOpenUpdate = (data: Tenant) => {
    setSelectTenant({...data});
    setOpenDrawerUpdate(true);
  };

  const handleOpenDetail = (data: Tenant) => {
    setSelectTenant({...data});
    setOpenDrawerDetail(true);
  };


  const handleSearch = () => {
    setFilterSearch(searchInput);
    setFilterStatus(status);
    setFilterCreatedDateFrom(createdDateFrom);
    setFilterCreatedDateTo(createdDateTo);
    setPageIndex(1); // Reset to first page on new search
  };

  const handleResetFilter = () => {
    setSearchInput("");
    setStatus(undefined);
    setCreatedDateFrom(undefined);
    setCreatedDateTo(undefined);

    setFilterSearch(undefined);
    setFilterStatus(undefined);
    setFilterCreatedDateFrom(undefined);
    setFilterCreatedDateTo(undefined);
    setPageIndex(1); // Reset to first page on reset
    setPageSize(10); // Optionally reset page size
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleOnPageChange = (page: number, pageSize: number) => {
    setPageIndex(page);
    setPageSize(pageSize);
  };

  const columns = [
    {
      title: "STT",
      key: "stt",
      width: 50,
      render: (_: any, __: any, index: number) => (
        <Text strong>{index + 1}</Text>
      ),
    },
    {
      title: "Mã đối tác",
      dataIndex: "domain",
      key: "domain",
      width: 200,
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: "Tên đối tác",
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: "Mã số thuế",
      dataIndex: "taxCode",
      key: "taxCode",
      width: 200,
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: "Số điện thoại",
      dataIndex: "phone",
      key: "phone",
      width: 200,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 200,
    },
    {
      title: "Url trang web",
      dataIndex: "website",
      key: "website",
      width: 200,
      render: (url: string) => (
        <a href={url} target="_blank" rel="noopener noreferrer">
          {url}
        </a>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      width: 200,
      render: (text: string) => <Text strong>{formatDateCustom(text)}</Text>,
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 200,
      render: (status: string) => (
        <Tag color={status === "ACTIVE" ? "green" : "red"}>
          {status === "ACTIVE" ? "Hoạt động" : "Không hoạt động"}
        </Tag>
      ),
    },
  ];

  const columnsAction = [
    {
      title: "Hành động",
      key: "action",
      width: 100,
      fixed: "right" as const,
      render: (data: Tenant) => (
        <Space
          size="middle"
          style={{display: "flex", justifyContent: "center"}}
        >
          <Button
            type="primary"
            onClick={() => handleOpenDetail(data)}
            icon={<InfoCircleOutlined />}
          />
          <Button
            type="primary"
            onClick={() => handleOpenUpdate(data)}
            icon={<EditOutlined />}
          />
        </Space>
      ),
    },
  ];

  useEffect(() => {
    console.log("Re-rendering TenantView with selectTenant:");
    refetch();
    return () => {
      setSelectTenant(null);
    }
  }, [openDrawerCreate, openDrawerUpdate, openDrawerDetail]);

  return (
    <BaseView>
      <CreateForm
        openDrawer={openDrawerCreate}
        setOpenDrawer={setOpenDrawerCreate}
      />
      <DetailView
        openDrawer={openDrawerDetail}
        setOpenDrawer={setOpenDrawerDetail}
        data={selectTenant}
      />
      <EditForm
        openDrawer={openDrawerUpdate}
        setOpenDrawer={setOpenDrawerUpdate}
        data={selectTenant}
      />

      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          marginBottom: 16,
        }}
      >
        {/* Bộ lọc bên trái */}
        <Space>
          <Input
            placeholder="Tìm kiếm đối tác..."
            prefix={<SearchOutlined />}
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            style={{width: 250}}
            onKeyDown={handleKeyDown}
          />
          <Select
            placeholder="Trạng thái"
            allowClear
            style={{width: 150}}
            value={status}
            onChange={(value) => setStatus(value)}
          >
            <Option value="ACTIVE">Hoạt động</Option>
            <Option value="INACTIVE">Không hoạt động</Option>
          </Select>
          <RangePicker
            style={{width: 280}}
            value={
              createdDateFrom && createdDateTo
                ? [dayjs(createdDateFrom), dayjs(createdDateTo)]
                : undefined
            }
            onChange={(dates) => {
              if (dates && dates.length === 2) {
                setCreatedDateFrom(dates[0]?.toISOString());
                setCreatedDateTo(dates[1]?.toISOString());
              } else {
                setCreatedDateFrom(undefined);
                setCreatedDateTo(undefined);
              }
            }}
          />
          <Button icon={<FilterOutlined />} onClick={handleSearch}>
            Lọc
          </Button>
          <Button icon={<FilterOutlined />} onClick={handleResetFilter}>
            Reset
          </Button>
        </Space>

        {/* Nút thêm */}
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleOpenCreate}
        >
          Thêm Đối tác
        </Button>
      </div>

      <BaseTable
        columns={[...columns, ...columnsAction]}
        data={data}
        total={total}
        isLoading={isLoading}
        onPageChange={handleOnPageChange}
        scroll={{x: 1000, y: "100%"}}
      />
    </BaseView>
  );
};
