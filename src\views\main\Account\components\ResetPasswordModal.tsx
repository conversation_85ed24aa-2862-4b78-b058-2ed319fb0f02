import React, {useEffect} from "react";
import {Modal, Form, Input} from "antd";
import useAccount from "~/hooks/account/useAccount";
import {notification} from "antd";
import {ListAccountDTO, AccountDTO} from "~/dto/account.dto";

interface ResetPasswordModalProps {
  open: boolean;
  onClose: () => void;
  data: AccountDTO; // hoặc userId tùy theo logic
}

export const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  open,
  onClose,
  data,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const {changePassword, refetch} = useAccount();

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      await changePassword({
        accountId: data.id,
        newPassword: values.password,
        confirmNewPassword: values.confirmPassword,
      });
      notification.success({message: "Đặt lại mật khẩu thành công"});
      form.resetFields();
      refetch();
      onClose();
    } catch (err: any) {
      notification.error({
        message: "Có lỗi xảy ra",
        description: err?.message || "Vui lòng thử lại",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (data) {
      form.setFieldsValue({data});
    }
  }, [data]);

  return (
    <Modal
      open={open}
      onCancel={onClose}
      onOk={() => form.submit()} // <-- submit form
      title="Đặt lại mật khẩu"
      okText="Xác nhận"
      cancelText="Hủy"
      confirmLoading={loading}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish} // <-- xử lý submit hợp lệ
      >
        <Form.Item
          label="Tài khoản"
          name="username"
          initialValue={data?.username}
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="Mật khẩu mới"
          name="password"
          rules={[{required: true, message: "Vui lòng nhập mật khẩu mới"}]}
        >
          <Input.Password />
        </Form.Item>

        <Form.Item
          label="Nhập lại mật khẩu"
          name="confirmPassword"
          dependencies={["password"]}
          rules={[
            {required: true, message: "Vui lòng nhập lại mật khẩu"},
            ({getFieldValue}) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value)
                  return Promise.resolve();
                return Promise.reject(new Error("Mật khẩu không khớp"));
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>
      </Form>
    </Modal>
  );
};
