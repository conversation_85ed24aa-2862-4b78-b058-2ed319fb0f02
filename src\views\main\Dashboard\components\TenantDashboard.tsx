import React, { useEffect } from "react";
import {Card, Table, Row, Col, Tooltip as AntdTooltip, Spin} from "antd";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import {Bar} from "react-chartjs-2";
import {InfoCircleOutlined} from "@ant-design/icons";
import useDashboard from "~/hooks/dashboard/useDashboard";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export const TenantDashboard: React.FC = () => {
  const {topTenantsData, isLoadingTopTenants} = useDashboard();
  const [barChartData, setBarChartData] = React.useState({
    labels: [],
    datasets: [],
  });

  useEffect(() => {
    if (topTenantsData) {
      const barChartData = {
        labels: topTenantsData.labels,
        datasets: [
          {
            label: "Số người dùng",
            data: topTenantsData.datasets,
            backgroundColor: "rgba(24, 144, 255, 0.7)", // Ant Design blue
            borderRadius: 6,
          },
        ],
      };
      setBarChartData(barChartData);
    }
  }, [topTenantsData]);

  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
    },
  };

  // Heatmap giả lập
  const days = ["T2", "T3", "T4", "T5", "T6", "T7", "CN"];
  const heatmapData = [
    {tenant: "Công ty ABC", data: [12, 14, 9, 13, 15, 11, 10]},
    {tenant: "BetaSoft", data: [5, 8, 7, 6, 9, 4, 3]},
    {tenant: "GoApp", data: [10, 12, 11, 10, 14, 13, 9]},
  ];

  const columns = [
    {
      title: "Tenant",
      dataIndex: "tenant",
      key: "tenant",
      fixed: "left" as const,
      width: 150,
    },
    ...days.map((day, i) => ({
      title: day,
      dataIndex: `day${i}`,
      key: `day${i}`,
      align: "center" as const,
      render: (value: number) => {
        const intensity = Math.min(255, value * 15);
        const bg = `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
        return (
          <div
            style={{
              backgroundColor: bg,
              color: "#000",
              borderRadius: 4,
              padding: "2px 6px",
              fontSize: 12,
            }}
          >
            {value}
          </div>
        );
      },
    })),
  ];

  const tableData = heatmapData.map((tenantData, index) => {
    const row: any = {
      key: index,
      tenant: tenantData.tenant,
    };
    tenantData.data.forEach((val, i) => {
      row[`day${i}`] = val;
    });
    return row;
  });

  return (
    <div>
      {/* Bar Chart */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card
            title="Top 5 Tenant có nhiều người dùng nhất"
            extra={
              <AntdTooltip title="Biểu đồ cột thể hiện số lượng người dùng của top Tenant có số lượng cao nhất.">
                <InfoCircleOutlined />
              </AntdTooltip>
            }
          >
            {
              isLoadingTopTenants ? <Spin /> : <Bar data={barChartData} options={barChartOptions} />  
            }
          </Card>
        </Col>

        {/* Heatmap (dùng Table) */}
        <Col xs={24} lg={12}>
          <Card
            title="Hoạt động theo ngày (Heat Map)"
            extra={
              <AntdTooltip title="Màu sắc thể hiện mức độ hoạt động theo từng ngày của mỗi Tenant. Màu càng đậm → hoạt động càng nhiều.">
                <InfoCircleOutlined />
              </AntdTooltip>
            }
          >
            <Table
              columns={columns}
              dataSource={tableData}
              pagination={false}
              bordered
              size="middle"
              scroll={{x: "max-content"}}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
