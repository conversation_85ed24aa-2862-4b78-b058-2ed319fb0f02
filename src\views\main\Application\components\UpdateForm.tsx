import React, {useEffect, useState} from "react";
import {
  Button,
  Drawer,
  Form,
  Input,
  Select,
  Space,
  Tag,
  message,
} from "antd";
import {
  CloseOutlined,
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import useApplication from "~/hooks/application/useApplication";
import {UpdateApplication} from "~/dto/application.dto";
import {systemHelper} from "~/common/helper/system.helper";

const {Option} = Select;

const UpdateForm = ({openDrawer, setOpenDrawer, data, isEdit}) => {
  const {updateApplication, isUpdating} = useApplication();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: UpdateApplication) => {
    setLoading(true);
    updateApplication(values).finally(() => {
      setOpenDrawer(false);
      setLoading(false);
    });
  };

  const handleClear = () => {
    form.resetFields();
  };

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
  };

  const generateClientId = () => {
    const code = form.getFieldValue("code");
    if (!code) {
      message.warning("Vui lòng nhập mã ứng dụng trước khi tạo Client ID");
      return;
    }
    const randomPart = systemHelper.generateClientId(code);
    form.setFieldsValue({clientId: randomPart});
  };

  const generateClientSecret = () => {
    const secret = systemHelper.generateClientSecret();
    form.setFieldsValue({clientSecret: secret});
  };

  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);
    }
  }, [data, form]);

  return (
    <Drawer
      title={isEdit ? "Chỉnh sửa ứng dụng" : "Xem chi tiết ứng dụng"}
      width={800}
      onClose={handleCloseDrawer}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
      extra={
        isEdit && (
          <Space>
            <Button onClick={handleClear} icon={<CloseOutlined />}>
              Clear
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
              loading={isUpdating}
            >
              Lưu
            </Button>
          </Space>
        )
      }
    >
      <Form
        layout="vertical"
        form={form}
        onFinish={handleFinish}
        disabled={!isEdit}
      >
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>

        <Form.Item name="code" label="Mã ứng dụng">
          <Input disabled />
        </Form.Item>

        <Form.Item
          name="name"
          label="Tên ứng dụng"
          rules={[{required: true, message: "Nhập tên ứng dụng"}]}
        >
          <Input placeholder="Nhập tên ứng dụng" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Mô tả"
        >
          <Input.TextArea rows={3} placeholder="Nhập mô tả ứng dụng" />
        </Form.Item>

        <Form.Item
          name="status"
          label="Trạng thái"
          rules={[{required: true, message: "Chọn trạng thái"}]}
        >
          <Select placeholder="Chọn trạng thái">
            <Option value="ACTIVE">Hoạt động</Option>
            <Option value="INACTIVE">Không hoạt động</Option>
          </Select>
        </Form.Item>

        {/* Client ID + Secret */}
        <Form.Item label="Client ID">
          <Space direction="horizontal">
            <Form.Item name="clientId" noStyle rules={[{required: true, message: "Nhập Client ID"}]}>
              <Input style={{width: 600}} disabled={!isEdit} />
            </Form.Item>
            <Button
              icon={<ReloadOutlined />}
              onClick={generateClientId}
              disabled={!isEdit}
            >
              Random
            </Button>
          </Space>
        </Form.Item>

        <Form.Item label="Client Secret">
          <Space direction="horizontal">
            <Form.Item name="clientSecret" noStyle rules={[{required: true, message: "Nhập Client Secret"}]}>
              <Input style={{width: 600}} disabled={!isEdit} />
            </Form.Item>
            <Button
              icon={<ReloadOutlined />}
              onClick={generateClientSecret}
              disabled={!isEdit}
            >
              Random
            </Button>
          </Space>
        </Form.Item>

        {/* Redirect URIs (editable) */}
        <Form.List name="redirectUris">
          {(fields, {add, remove}) => (
            <Form.Item label="Redirect URIs">
              <Space direction="vertical" style={{width: "100%"}}>
                {fields.map((field) => (
                  <Space
                    key={field.key}
                    align="baseline"
                    style={{width: "100%"}}
                  >
                    <Form.Item
                      {...field}
                      name={[field.name]}
                      rules={[{required: true, message: "Nhập redirect URI"}]}
                      style={{flex: 1}}
                    >
                      <Input
                        placeholder="Nhập redirect URI"
                        style={{width: "600px"}}
                      />
                    </Form.Item>
                    {isEdit && (
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(field.name)}
                      />
                    )}
                  </Space>
                ))}
                {isEdit && (
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusOutlined />}
                    block
                  >
                    Thêm URI
                  </Button>
                )}
              </Space>
            </Form.Item>
          )}
        </Form.List>

        {/* Tenants (chỉ hiển thị) */}
        <Form.Item
          shouldUpdate={(prev, curr) => prev.tenants !== curr.tenants}
          noStyle
        >
          {() => {
            const tenants = form.getFieldValue("tenants") || [];
            return (
              <Form.Item label={`Danh sách tenant (${tenants.length})`}>
                <div>
                  {tenants.map((t) => (
                    <Tag key={t.domain}>{t.name}</Tag>
                  ))}
                </div>
              </Form.Item>
            );
          }}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default UpdateForm;
