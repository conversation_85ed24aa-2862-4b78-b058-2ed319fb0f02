import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import React from "react";
import "./index.less";

interface IProps {
  sidebarCollapsed: boolean;
  toggleSiderBar: () => void
}
const Hamburger = (props: IProps) => {
  const { sidebarCollapsed, toggleSiderBar } = props;
  return (
    <div className="hamburger-container">
      {sidebarCollapsed ? <MenuUnfoldOutlined onClick={toggleSiderBar} /> : <MenuFoldOutlined onClick={toggleSiderBar} />}
    </div>
  );
};

export default Hamburger;
