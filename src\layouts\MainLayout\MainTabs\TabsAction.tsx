import type { <PERSON> } from "react";
import { useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useLayoutConfig } from "~/stores/layoutConfig";

// Map tab titles to modal types
const MODAL_MAP = {
  "list-license": "license",
  "list-customer": "customer",
  "list-product": "product",
} as const;

type ModalType = (typeof MODAL_MAP)[keyof typeof MODAL_MAP];

const TabsAction: FC = () => {
  const { tabs, selectedKey } = useLayoutConfig();
  const [openModal, setOpenModal] = useState<ModalType | null>(null);

  const currentTab = tabs.find((tab) => tab.key === selectedKey);

  const modalType = currentTab?.rootPath
    ? MODAL_MAP[currentTab.rootPath]
    : null;

  const handlePress = () => {
    if (modalType) {
      setOpenModal(modalType);
    } else {
      console.log("No modal defined for this tab:", currentTab?.title);
    }
  };

  const handleCloseModal = () => {
    setOpenModal(null);
  };

  const handleSuccess = () => {
    console.log(`${currentTab?.title} created successfully!`);
    // Có thể refresh data hoặc thực hiện actions khác
  };

  return (
    <>
      {currentTab?.title !== "Báo Cáo" && (
        <Button
          onClick={handlePress}
          type="primary"
          htmlType="submit"
          icon={<PlusOutlined />}
          disabled={!modalType}
        >
          {`Tạo ${currentTab?.title || ""}`}
        </Button>
      )}

    </>
  );
};

export default TabsAction;
