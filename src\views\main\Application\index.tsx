import React, {useEffect, useState} from "react";
import {
  Card,
  Tag,
  Typography,
  Space,
  Button,
  Row,
  Col,
  Input,
  Spin,
} from "antd";
import {
  EyeOutlined,
  SettingOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import CreateForm from "./components/CreateForm";
import UpdateForm from "./components/UpdateForm";
import DetailForm from "./components/DetailForm";
import BaseView from "~/components/BaseView";
import {ApplicationItem as AppItem} from "~/dto/application.dto";
import useApplication from "~/hooks/application/useApplication";

const {Title, Text} = Typography;

export const Application: React.FC = () => {
  const [openDrawerCreate, setOpenDrawerCreate] = React.useState(false);
  const [openDrawerUpdate, setOpenDrawerUpdate] = React.useState(false);
  const [openDrawerDetail, setOpenDrawerDetail] = React.useState(false);

  const [selectedApp, setSelectedApp] = React.useState<AppItem | null>(null);
  const [filter, setFilter] = useState({
    pageIndex: 1,
    pageSize: 10,
    searchValue: "",
  });
  const {data, isLoading, refetch} = useApplication({
    ...filter,
  });
  const [isEdit, setIsEdit] = React.useState(true);

  const handleSelectApp = (app: AppItem) => {
    setSelectedApp(app);
    setOpenDrawerDetail(true);
    setIsEdit(false);
  };

  const generateColorStatus = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "green";
      case "INACTIVE":
        return "red";
      default:
        return "green";
    }
  };

  const handleUpdateApp = (app: AppItem) => {
    setSelectedApp(app);
    setOpenDrawerUpdate(true);
    setIsEdit(true);
  };

  const handleSearch = (value: string) => {
    // filter data
    setFilter({...filter, searchValue: value, pageIndex: 1});
    refetch();
  };

  useEffect(() => {}, [isEdit]);

  return (
    <BaseView>
      <CreateForm
        openDrawer={openDrawerCreate}
        setOpenDrawer={setOpenDrawerCreate}
      />
      <UpdateForm
        openDrawer={openDrawerUpdate}
        setOpenDrawer={setOpenDrawerUpdate}
        data={selectedApp}
        isEdit={isEdit}
      />
      <DetailForm
        openDrawer={openDrawerDetail}
        setOpenDrawer={setOpenDrawerDetail}
        data={selectedApp}
      />

      <Space
        style={{
          width: "100%",
          justifyContent: "space-between",
          marginBottom: 12,
        }}
      >
        {/* Search App */}
        <Space>
          <Input
            placeholder="Tìm kiếm ứng dụng..."
            prefix={<SearchOutlined />}
            style={{width: 250}}
            value={filter.searchValue}
            onChange={(e) => setFilter({...filter, searchValue: e.target.value})}
          />
          <Button icon={<FilterOutlined />} onClick={() => handleSearch(filter.searchValue)}>
            Lọc
          </Button>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setOpenDrawerCreate(true)}
        >
          Thêm ứng dụng
        </Button>
      </Space>

      <Row
        gutter={[16, 16]}
        style={{overflowY: "auto", height: "calc(100vh - 200px"}}
      >
        {!isLoading ? (
          data?.map((app, idx) => (
            <Col key={idx} md={8}>
              <Card
                title={
                  <Space size="small">
                    <Text strong>{app?.name}</Text>
                    <Tag bordered={false}>v.{app.version}</Tag>
                  </Space>
                }
                bordered
              >
                <Text type="secondary">{app.description}</Text>

                <div style={{margin: "12px 0"}}>
                  <Text strong>Tenant sử dụng:</Text>
                  <div style={{marginTop: 4}}>
                    {app?.tenants?.length > 0 ? (
                      app?.tenants?.map((t) => (
                        <Tag key={t?.domain} color="default">
                          {t?.name}
                        </Tag>
                      ))
                    ) : (
                      <Tag color="default">Chưa có tenant</Tag>
                    )}
                  </div>
                </div>

                <div style={{marginBottom: 12}}>
                  <Text strong>Trạng thái:</Text>{" "}
                  <Tag color={generateColorStatus(app.status)}>
                    {app.status}
                  </Tag>
                </div>

                <Space>
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => handleUpdateApp(app)}
                  >
                    Chỉnh sửa
                  </Button>
                  <Button
                    icon={<EyeOutlined />}
                    type="primary"
                    onClick={() => handleSelectApp(app)}
                  >
                    Chi tiết
                  </Button>
                </Space>
              </Card>
            </Col>
          ))
        ) : (
          <Space style={{width: "100%", justifyContent: "center"}}>
            <Spin></Spin>
          </Space>
        )}
      </Row>
    </BaseView>
  );
};
