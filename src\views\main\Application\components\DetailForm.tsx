import React, {useEffect, useState} from "react";
import {<PERSON><PERSON>, Drawer, Descriptions, Space, Tag, Typography, Divider} from "antd";
import {CloseOutlined} from "@ant-design/icons";
import moment from "moment";

const {Paragraph, Text} = Typography;

const DetailView = ({openDrawer, setOpenDrawer, data}) => {
  const [detail, setDetail] = useState(null);

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
  };

  useEffect(() => {
    if (data) {
      setDetail(data);
    }
  }, [data]);

  return (
    <Drawer
      title="Chi tiết ứng dụng"
      width={800}
      onClose={handleCloseDrawer}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
    >
      {detail && (
        <Descriptions
          bordered
          column={2}
          size="middle"
          layout="horizontal"
          labelStyle={{width: 200, fontWeight: 500}}
        >
          <Descriptions.Item label="Mã ứng dụng">
            {detail.code}
          </Descriptions.Item>
          <Descriptions.Item label="Tên ứng dụng">
            {detail.name}
          </Descriptions.Item>

          <Descriptions.Item label="Client ID" span={2}>
            <Paragraph copyable>{detail.clientId}</Paragraph>
          </Descriptions.Item>

          <Descriptions.Item label="Client Secret" span={2}>
            <Paragraph copyable ellipsis={{rows: 1, expandable: true}}>
              {detail.clientSecret}
            </Paragraph>
          </Descriptions.Item>

          <Descriptions.Item label="Mô tả" span={2}>
            <Paragraph>{detail.description}</Paragraph>
          </Descriptions.Item>

          <Descriptions.Item label="Trạng thái">
            {detail.status === "ACTIVE" ? (
              <Tag color="green">Hoạt động</Tag>
            ) : (
              <Tag color="red">Không hoạt động</Tag>
            )}
          </Descriptions.Item>

          <Descriptions.Item label="Phiên bản">
            {detail.version}
          </Descriptions.Item>

          <Descriptions.Item label="Ngày tạo">
            {moment(detail.createdDate).format("DD/MM/YYYY HH:mm:ss")}
          </Descriptions.Item>

          <Descriptions.Item label="Ngày cập nhật">
            {moment(detail.updatedDate).format("DD/MM/YYYY HH:mm:ss")}
          </Descriptions.Item>

          <Descriptions.Item label="Người tạo">
            {detail.createdBy || <Text type="secondary">N/A</Text>}
          </Descriptions.Item>
          <Descriptions.Item label="Người cập nhật">
            {detail.updatedBy || <Text type="secondary">N/A</Text>}
          </Descriptions.Item>

          <Descriptions.Item label="Redirect URIs" span={2}>
            <Space direction="vertical">
              {detail.redirectUris?.map((uri, index) => (
                <Paragraph key={index} copyable style={{marginBottom: 0}}>
                  {uri}
                </Paragraph>
              ))}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      )}
      <Divider />
      <Text strong style={{display: "block", marginTop: 16}}>
        Danh sách Tenant sử dụng ứng dụng:
      </Text>
      <Descriptions column={1} style={{marginTop: 8}}>
        {detail?.tenants?.map((tenant) => (
          <Descriptions.Item
            key={tenant.id}
            style={{border: "1px solid #f0f0f0", padding: 8, borderRadius: 4}}
          >
            <Tag color="blue">{tenant.name}</Tag>
            <div>
              <Text strong>Domain:</Text> {tenant.domain}
            </div>
            {tenant.email && (
              <div>
                <Text strong>Email:</Text> {tenant.email}
              </div>
            )}
            {tenant.website && (
              <div>
                <Text strong>Website:</Text> {tenant.website}
              </div>
            )}
            {tenant.phone && (
              <div>
                <Text strong>Phone:</Text> {tenant.phone}
              </div>
            )}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </Drawer>
  );
};

export default DetailView;
