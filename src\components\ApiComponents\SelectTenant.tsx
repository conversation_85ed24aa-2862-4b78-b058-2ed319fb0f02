import React, { useEffect, useMemo, useState } from "react";
import { Select, Spin } from "antd";
import useTenant from "~/hooks/tenant/useTenant";

type SelectTenantProps = {
  onChange: (value: string | string[]) => void;
  value?: string | string[];
  styles?: React.CSSProperties;
  mode?: "multiple" | "tags";
};

const SelectTenant = ({
  onChange,
  value,
  styles = {},
  mode,
}: SelectTenantProps) => {
  const [name, setName] = useState("");

  const { data, isLoading } = useTenant(
    {
      name,
      pageIndex: 1,
      pageSize: 20, // load nhiều hơn để đảm bảo value có trong options
    },
    true
  );

  const handleOnNameChange = (name: any) => {
    setName(name);
  };

  // Convert data to options format
  const options = useMemo(() => {
    return data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }, [data]);

  // Determine value default for single/multi
  const selectValue =
    mode === "multiple" || mode === "tags"
      ? (value as string[] | undefined) || []
      : (value as string | undefined) || undefined;

  const handleOnChange = (val: string | string[]) => {
    onChange(val);
  };

  return (
    <Select
      mode={mode}
      showSearch
      placeholder="Chọn Tenant"
      style={{ width: "400px", ...styles }}
      loading={isLoading}
      options={options}
      value={selectValue}
      onChange={handleOnChange}
      onSearch={(e: any) => handleOnNameChange(e)}
      allowClear
      notFoundContent={isLoading ? <Spin size="small" /> : "Không có dữ liệu"}
    />
  );
};

export default SelectTenant;
