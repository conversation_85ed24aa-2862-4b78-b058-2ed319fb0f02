import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Select, Spin } from "antd";
import useTenant from "~/hooks/tenant/useTenant";
import helper from "~/common/helper/helper";

type SelectTenantProps = {
  onChange: (value: string | string[]) => void;
  value?: string | string[];
  styles?: React.CSSProperties;
  mode?: "multiple" | "tags";
};

const SelectTenant = ({
  onChange,
  value,
  styles = {},
  mode,
}: SelectTenantProps) => {
  const [name, setName] = useState("");
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { data, isLoading } = useTenant(
    {
      name,
      pageIndex: 1,
      pageSize: 20, // load nhiều hơn để đảm bảo value có trong options
    },
    true
  );
  const { debounceInput } = helper;

  // Debounced function để cập nhật name sau 500ms

  const handleOnNameChange = (searchTerm: any) => {
    debounceInput(searchTerm, setName, debounceTimeoutRef);
  };

  // Convert data to options format
  const [options, setOptions] = useState<any>();

  useEffect(() => {
    if (!data || data.length === 0) return;
    const newData = data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    console.log("data", newData);

    setOptions(newData);
  }, [name]);

  // Determine value default for single/multi
  const selectValue =
    mode === "multiple" || mode === "tags"
      ? (value as string[] | undefined) || []
      : (value as string | undefined) || undefined;

  const handleOnChange = (val: string | string[]) => {
    onChange(val);
  };

  return (
    <Select
      mode={mode}
      showSearch
      placeholder="Chọn Tenant"
      style={{ width: "400px", ...styles }}
      loading={isLoading}
      options={options}
      value={selectValue}
      onChange={handleOnChange}
      onSearch={(e: any) => handleOnNameChange(e)}
      allowClear
      notFoundContent={isLoading ? <Spin size="small" /> : "Không có dữ liệu"}
    />
  );
};

export default SelectTenant;
