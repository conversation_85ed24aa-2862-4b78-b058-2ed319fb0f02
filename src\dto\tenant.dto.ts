import { PageRequest } from "~/@ui/GridControl/models";

export interface ListTenantRes {
    data: TenantItem[];
    total: number;
}

export interface TenantItem {
    id: string;
    createdDate: string;
    updatedDate: string;
    createdBy: any;
    updatedBy: any;
    name: string;
    domain: string;
    status: string;
    website: string;
}

export interface CreateTenantAppReq {
  code: string;
  redirectUris: string[];
}

export interface RegisterTenantReq {
  name: string;
  domain: string;
  rootUsername: string;
  rootPassword: string;
  rootFullName: string;
  website: string;
  applications?: CreateTenantAppReq[];
}

// Enum EStatus giống như bên NestJS
export enum TenantStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface ListTenantReq extends PageRequest {
  domain?: string;
  name?: string;
  status?: string;
  createdDateFrom?: string;
  createdDateTo?: string;
  website?: string;
  email?: string;
  phone?: string;
  taxCode?: string;
  address?: string;
  tenantId?: string;
  searchValue?: string;
}

// Update
export interface UpdateTenantReq {
  id: string;
  name: string;
  domain: string;
  rootUsername: string;
  rootFullName: string;
  website: string;
  applications?: CreateTenantAppReq[];
  status: TenantStatus;
}
