import { IEvmChainConfig } from "./chain-list";
import * as envs from "./envs";

export interface IEnvConfig {
  name?: string;
  CONNECTORS: {
    ROOT: {
      baseUrl: string;
    };
  };
}
let envConfig: IEnvConfig = undefined;
export function configEnv(): IEnvConfig {
  if (envConfig) {
    return envConfig;
  }
  const envName = process.env.REACT_APP_ENV || "dev";
  const currentConfig = envs[envName];
  return {
    ...currentConfig,
    name: envName,
  };
}
