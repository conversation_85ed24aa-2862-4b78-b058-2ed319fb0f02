import { createElement, FC } from "react";
import {
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from "@ant-design/icons";
import { Dropdown, Layout, theme as antTheme, Tooltip } from "antd";
import { useNavigate } from "react-router-dom";
import logo from "~/assets/logo/logo.jpg";
import { LocaleFormatter, useLocale } from "~/locales";
import { useThemeStore } from "~/stores/themeStore";
import { useAuthStore } from "~/stores/authStore";
import { useLayoutConfig } from "~/stores/layoutConfig";
import FullScreen from "~/components/FullScreen";
import BaseText from "~/components/BaseText";
import { ReactComponent as MoonSvg } from "~/assets/header/moon.svg";
import { ReactComponent as SunSvg } from "~/assets/header/sun.svg";

const { Header } = Layout;

type IMainHeaderProps = {
  collapsed: boolean;
  toggle: () => void;
};

type Action = "userInfo" | "userSetting" | "logout";

const MainHeader: FC<IMainHeaderProps> = ({
  collapsed,
  toggle,
}: IMainHeaderProps) => {
  const token = antTheme.useToken();
  const { themeStyle, changeTheme } = useThemeStore();
  const { logged, logout, userInfo } = useAuthStore();
  const navigate = useNavigate();
  const { formatMessage } = useLocale();

  const { device } = useLayoutConfig();

  const toLogin = () => {
    navigate("/login");
  };

  const onChangeTheme = () => {
    const newTheme = themeStyle === "dark" ? "light" : "dark";

    localStorage.setItem("theme", newTheme);
    changeTheme({ themeStyle: newTheme });
  };

  return (
    <Header
      className="layout-page-header bg-2"
      style={{ backgroundColor: token.token.colorBgContainer }}
    >
      {device !== "MOBILE" && (
        <div className="logo" style={{ width: collapsed ? 80 : 200 }}>
          <img
            src={logo}
            alt=""
            style={{ marginRight: collapsed ? "2px" : "20px", width: 35, height: 35 }}
          />
        </div>
      )}
      <div className="layout-page-header-main">
        <div onClick={toggle}>
          <span id="sidebar-trigger">
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </span>
        </div>
        <div className="actions">
          <Tooltip
            title={formatMessage({
              id:
                themeStyle === "dark"
                  ? "gloabal.tips.theme.lightTooltip"
                  : "gloabal.tips.theme.darkTooltip",
            })}
          >
            <span>
              {createElement(themeStyle === "dark" ? SunSvg : MoonSvg, {
                onClick: onChangeTheme,
              })}
            </span>
          </Tooltip>
          <FullScreen />

          {logged ? (
            <Dropdown
              menu={{
                items: [
                  {
                    key: "1",
                    icon: <LogoutOutlined />,
                    label: (
                      <BaseText onClick={logout} style={{ cursor: "pointer" }}>
                        <LocaleFormatter id="header.avator.logout" />
                      </BaseText>
                    ),
                  },
                ],
              }}
            >
              <span className="user-action">
                <img
                  src={userInfo?.avatar || logo}
                  className="user-avatar"
                  alt="avator"
                />
              </span>
            </Dropdown>
          ) : (
            <BaseText style={{ cursor: "pointer", marginLeft: 20 }} onClick={toLogin}>
              <img
                  src={userInfo?.avatar || logo}
                  className="user-avatar"
                  alt="avator"
                />
            </BaseText>
          )}
        </div>
      </div>
    </Header>
  );
};
export default MainHeader;
