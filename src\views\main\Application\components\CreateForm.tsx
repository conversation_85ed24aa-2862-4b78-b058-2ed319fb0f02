import React from "react";
import {Button, Form, Input, Select, Space, Drawer, Row, Col} from "antd";
import {
  CloseOutlined,
  SaveOutlined,
  ReloadOutlined,
  PlusOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons";
import useApplication from "~/hooks/application/useApplication";
import {systemHelper} from "~/common/helper/system.helper";

const {Option} = Select;

const CreateForm = ({openDrawer, setOpenDrawer}) => {
  const {createApplication, isCreating} = useApplication();
  const [domain, setDomain] = React.useState<string>("");
  const [form] = Form.useForm();

  const handleCreateApp = (values: any) => {
    createApplication(values).finally(() => setOpenDrawer(false));
  };

  const handleSubmit = () => {
    form.submit();
  };

  const handleClear = () => {
    form.resetFields();
  };

  const generateClientId = (domain: string) => {
    const clientId = systemHelper.generateClientId(domain);
    form.setFieldValue("clientId", clientId);
  };

  const generateClientSecret = () => {
    const secret = systemHelper.generateClientSecret();
    form.setFieldValue("clientSecret", secret);
  };

  return (
    <Drawer
      title="Tạo ứng dụng mới"
      width={800}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
      extra={
        <Space>
          <Button onClick={handleClear} icon={<CloseOutlined />}>
            Clear
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSubmit}
            loading={isCreating}
          >
            Lưu
          </Button>
        </Space>
      }
    >
      <Form
        layout="vertical"
        form={form}
        onFinish={handleCreateApp}
        initialValues={{
          status: "ACTIVE",
        }}
      >
        <Form.Item
          name="code"
          label="Mã ứng dụng"
          rules={[
            {required: true, message: "Nhập mã ứng dụng"},
            {
              pattern: /^[a-zA-Z0-9-]+$/,
              message: "Mã ứng dụng không được chứa ký tự đặc biệt và khoảng trắng",
            },
          ]}
        >
          <Input
            placeholder="Nhập mã ứng dụng"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
          />
        </Form.Item>

        <Form.Item
          name="name"
          label="Tên ứng dụng"
          rules={[{required: true, message: "Nhập tên ứng dụng"}]}
        >
          <Input placeholder="Nhập tên ứng dụng" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Mô tả"
          rules={[{required: true, message: "Nhập mô tả"}]}
        >
          <Input.TextArea rows={3} placeholder="Nhập mô tả ứng dụng" />
        </Form.Item>

        <Form.Item
          name="status"
          label="Trạng thái"
          rules={[{required: true, message: "Chọn trạng thái"}]}
        >
          <Select placeholder="Chọn trạng thái">
            <Option value="ACTIVE">Hoạt động</Option>
            <Option value="INACTIVE">Không hoạt động</Option>
          </Select>
        </Form.Item>

        {/* Client ID */}
        <Form.Item label="Client ID" required>
          <Space>
            <Form.Item
              name="clientId"
              noStyle
              rules={[{required: true, message: "Nhập Client ID"}]}
            >
              <Input style={{width: 600}} placeholder="Client ID" />
            </Form.Item>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => generateClientId(domain)}
            >
              Tạo
            </Button>
          </Space>
        </Form.Item>

        {/* Client Secret */}
        <Form.Item label="Client Secret" required>
          <Space>
            <Form.Item
              name="clientSecret"
              noStyle
              rules={[{required: true, message: "Nhập Client Secret"}]}
            >
              <Input.Password
                style={{width: 600}}
                placeholder="Client Secret"
              />
            </Form.Item>
            <Button icon={<ReloadOutlined />} onClick={generateClientSecret}>
              Tạo
            </Button>
          </Space>
        </Form.Item>

        {/* Redirect URIs */}
        <Form.Item label="Redirect URIs" required>
          <Form.List
            name="redirectUris"
            rules={[
              {
                validator: async (_, uris) => {
                  if (!uris || uris.length === 0) {
                    return Promise.reject(
                      new Error("Nhập ít nhất một Redirect URI")
                    );
                  }
                },
              },
            ]}
          >
            {(fields, {add, remove}) => (
              <>
                {fields.map(({key, name, ...restField}) => (
                  <Space
                    key={key}
                    style={{display: "flex", marginBottom: 8}}
                    align="baseline"
                  >
                    <Form.Item
                      {...restField}
                      name={name}
                      rules={[{required: true, message: "Nhập Redirect URI"}]}
                    >
                      <Input
                        placeholder="https://your-app.com/callback"
                        style={{width: 500}}
                      />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  Thêm URI
                </Button>
              </>
            )}
          </Form.List>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default CreateForm;
