import React from "react";
import {Tag, Space, Tooltip, Descriptions} from "antd";
import {CopyOutlined} from "@ant-design/icons";
import {notification} from "antd";

export const TenantClient = ({data, code}: any) => {
  const app = data?.applications?.find((app: any) => app.code === code);
  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      notification.success({message: "Copied"});
    } catch (err) {
      notification.error({message: "Co<PERSON> failed"});
    }
  };
  return (
    <Descriptions
      bordered
      size="small"
      column={1}
      labelStyle={{fontWeight: "bold", width: 150}}
    >
      {app?.clientId && (
        <Descriptions.Item label="Client ID">
          {app.clientId}{" "}
          <Tooltip title="Sao chép Client ID">
            <CopyOutlined
              style={{marginLeft: 8, float: 'right', cursor: "pointer"}}
              onClick={() => handleCopy(app.clientId)}
            />
          </Tooltip>
        </Descriptions.Item>
      )}
      {app?.clientSecret && (
        <Descriptions.Item
          label="Client Secret"
        >
          {app.clientSecret}{" "}
          <Tooltip title="Sao chép Client Secret">
            <CopyOutlined
              style={{marginLeft: 'auto', float: 'right', cursor: "pointer"}}
              onClick={() => handleCopy(app.clientSecret)}
            />
          </Tooltip>
        </Descriptions.Item>
      )}
    </Descriptions>
  );
};
