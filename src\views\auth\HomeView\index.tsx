import React from "react";
import { Button, Checkbox, Form, Input, Layout, Spin, Typography } from "antd";
import { useAuthStore } from "~/stores/authStore";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { COLORS } from "~/common/constants";
import { loginAssets } from "~/assets";
import BaseText from "~/components/BaseText";

const { Title } = Typography;

// TypeScript interfaces
interface HomeFormValues {
  username: string;
  password: string;
  remember?: boolean;
}

// Constants

// Styles
const styles = {
  layout: {
    height: "100vh",
    justifyContent: "center" as const,
    alignItems: "flex-end" as const,
    backgroundImage: `url(${loginAssets.loginBackground})`,
    backgroundSize: "cover" as const,
    backgroundRepeat: "no-repeat" as const,
    backgroundPosition: "center" as const,
    paddingRight: 100,
  },
  formContainer: {
    backgroundColor: COLORS.WHITE,
    padding: "20px 24px",
    borderRadius: 12,
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
    width: 360,
  },
  title: {
    textAlign: "center" as const,
    marginBottom: 24,
    color: COLORS.PRIMARY_2,
  },
  submitButton: {
    backgroundColor: COLORS.PRIMARY_2,
    borderColor: COLORS.PRIMARY_2,
    width: "100%",
    height: 40,
  },
} as const;

const HomeView: React.FC = () => {
  const { login, isLoading } = useAuthStore();

  const handleFinish = (values: HomeFormValues) => {
    login({
      username: values.username,
      password: values.password,
    });
  };

  return (
    <Layout style={styles.layout}>
      <Spin tip="Logging in..." spinning={isLoading}>
        <div style={styles.formContainer}>
          <Title level={2} style={styles.title}>
            Welcome Back
          </Title>

          <Form<HomeFormValues>
            name="loginForm"
            layout="vertical"
            initialValues={{ remember: true }}
            onFinish={handleFinish}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              label="Email"
              name="email"
              rules={[
                {
                  required: true,
                  message: "Please enter your email",
                },
                {
                  min: 3,
                  message: "Email must be at least 3 characters",
                },
              ]}
            >
              <Input placeholder="Enter your email" />
            </Form.Item>

            <Form.Item
              label="Password"
              name="password"
              rules={[
                {
                  required: true,
                  message: "Please enter your password",
                },
                {
                  min: 6,
                  message: "Password must be at least 6 characters",
                },
              ]}
            >
              <Input.Password placeholder="Enter your password" />
            </Form.Item>

            <Form.Item name="remember" valuePropName="checked">
              <Checkbox>
                <BaseText>Remember me</BaseText>
              </Checkbox>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                style={styles.submitButton}
                loading={isLoading}
              >
                Sign In
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Spin>
    </Layout>
  );
};

export default HomeView;
