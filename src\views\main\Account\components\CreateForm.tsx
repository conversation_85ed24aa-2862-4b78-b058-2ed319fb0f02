import React, {useState} from "react";
import {Button, Form, Input, Select, Space, Drawer, Upload} from "antd";
import {CameraOutlined, CloseOutlined, SaveOutlined} from "@ant-design/icons";
import SelectTenant from "~/components/ApiComponents/SelectTenant";
import useAccount from "~/hooks/account/useAccount";
import useUploadSingleS3 from "~/hooks/uploadFile/useUploadSingle";

const {Option} = Select;

const CreateForm = ({openDrawer, setOpenDrawer}) => {
  const [form] = Form.useForm();
  const [domain, setDomain] = useState<string | undefined>("");
  const {createAccount} = useAccount();
  const {mutateAsync: uploadFile} = useUploadSingleS3();

  const handleCreateApp = (values: any) => {
    console.log("Submitted values:", values);
  };

  const handleSubmit = () => {
    form.submit();
  };

  const handleClear = () => {
    form.resetFields();
  };

  const handleOnChangeTenant = (value: string | string[]) => {
    form.setFieldsValue({tenant: value});
    setDomain(value as string);
  };

  const handleUpload = async (info: any) => {
    if (info.fileList.length === 0) return;

    const file = info.fileList[0].originFileObj;
    if (!file) return;

    const formData = new FormData();
    formData.append("file", file);

    try {
      const res = await uploadFile(formData);
      console.log(res);
    } catch (error) {
      console.error("Upload failed:", error);
    }
  };

  return (
    <Drawer
      title="Tạo tài khoản mới"
      width={800}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
      extra={
        <Space>
          <Button onClick={handleClear} icon={<CloseOutlined />}>
            Clear
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSubmit}>
            Lưu
          </Button>
        </Space>
      }
    >
      <Space
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center", // Center
          marginBottom: 16,
        }}
      >
        <Upload
          beforeUpload={() => false}
          accept="image/*"
          showUploadList={false}
          fileList={[]}
          onChange={handleUpload}
        >
          <Button type="primary" icon={<CameraOutlined />} size="large" />
        </Upload>
      </Space>
      <Form layout="vertical" form={form} onFinish={handleCreateApp}>
        <Form.Item
          name="tenant"
          label="Tenant"
          rules={[{required: true, message: "Chọn tenant"}]}
        >
          <SelectTenant onChange={handleOnChangeTenant} />
        </Form.Item>
        <Form.Item
          name="name"
          label="Tên tài khoản"
          rules={[{required: true, message: "Nhập tên tài khoản"}]}
        >
          <Input placeholder="Nhập tên tài khoản" addonAfter={`@${domain}`} />
        </Form.Item>
        <Form.Item name="email" label="Email">
          <Input placeholder="Nhập email" />
        </Form.Item>
        <Form.Item
          name="username"
          label="Tên đăng nhập"
          rules={[{required: true, message: "Nhập tên đăng nhập"}]}
        >
          <Input placeholder="Nhập tên đăng nhập" />
        </Form.Item>
        <Form.Item
          name="password"
          label="Mật khẩu"
          rules={[{required: true, message: "Nhập mật khẩu"}]}
        >
          <Input.Password placeholder="Nhập mật khẩu" />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          label="Xác nhận mật khẩu"
          rules={[{required: true, message: "Xác nhận mật khẩu"}]}
        >
          <Input.Password placeholder="Xác nhận mật khẩu" />
        </Form.Item>
        <Form.Item
          name="status"
          label="Trạng thái"
          rules={[{required: true, message: "Chọn trạng thái"}]}
        >
          <Select placeholder="Chọn trạng thái" defaultValue={"ACTIVE"}>
            <Option value="ACTIVE">Hoạt động</Option>
            <Option value="INACTIVE">Không hoạt động</Option>
          </Select>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default CreateForm;
