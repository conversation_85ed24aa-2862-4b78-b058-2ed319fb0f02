
import { v4 } from 'uuid';

const SPERATOR_USERNAME = '@';
const SPERATOR_CLIENT_ID = '_';
const uuidNoDash = () => {
  return v4().replace(/-/g, '').toUpperCase();
};
const generateClientId = (applicationCode: string, tenantCode: string = '') => {
    if (tenantCode) {
        return `${applicationCode}${SPERATOR_CLIENT_ID}${tenantCode}${SPERATOR_CLIENT_ID}${uuidNoDash().toLowerCase()}`;
    }
    return `${applicationCode}${SPERATOR_CLIENT_ID}${uuidNoDash().toLowerCase()}`;
};
const generateClientSecret = () => {
    return `${uuidNoDash()}${uuidNoDash()}`.toLowerCase();
};

const generateUserNameAccount = (refixUserName: string, domain: string) => {
    return `${refixUserName}${SPERATOR_USERNAME}${domain}`;
};

const getTenantDomainByUserName = (userName: string) => {
    const tenantDomain = userName.split(SPERATOR_USERNAME)[1];
    return tenantDomain;
};


const generateQueryStringUrl = ({
    redirectUri,
    clientId,
    state,
}: {
    redirectUri: string;
    clientId: string;
    state?: string;
}) => {
    const obj = {
        client_id: clientId,
        redirect_uri: redirectUri,
        state: state || '',
    };
    const queryString = new URLSearchParams(obj).toString();
    return queryString;
};

export const systemHelper = {
    generateClientId,
    generateClientSecret,
    generateUserNameAccount,
    getTenantDomainByUserName,
    generateQueryStringUrl,
};
