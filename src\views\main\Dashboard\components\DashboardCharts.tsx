// src/components/Charts.tsx
import { Card } from 'antd';
import React from 'react';
import { TenantDashboard } from './TenantDashboard';
import { AccountDashboard } from './AccountDashboard';
import { ApplicationDashboard } from './ApplicationDashboard';
import {ReportView} from '../../Report';

export const DashboardCharts: React.FC = () => {

  return (
    <div style={{ marginTop: 32 }}>
      <Card title="Thống kê Tenant" style={{ marginBottom: 24 }}>
          <TenantDashboard />
      </Card>
      <Card title="Thống kê người dùng">
          <AccountDashboard />
      </Card>
      <Card title="Thống kê ứng dụng" style={{ marginTop: 24 }}>
          <ApplicationDashboard />
      </Card>
      <Card title="Báo cáo" style={{ marginTop: 24 }}>
          <ReportView />
      </Card>
    </div>
  );
};
