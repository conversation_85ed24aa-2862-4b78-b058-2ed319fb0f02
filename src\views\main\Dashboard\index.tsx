// src/pages/Dashboard.tsx
import React from "react";
import {
  Card,
  Col,
  Row,
  Typography,
  Space,
  Spin,
  Tooltip as AntdTooltip, 
} from "antd";
import {
  AppstoreOutlined,
  UserOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {DashboardCharts} from "./components/DashboardCharts";
import BaseView from "~/components/BaseView";
import useDashboard from "~/hooks/dashboard/useDashboard";

const {Title, Text} = Typography;

export const Dashboard: React.FC = () => {
  // Here you would typically fetch data using a hook like useDashboard
  const {statsData, isLoadingStats, topTenantsData, isLoadingTopTenants} =
    useDashboard();

  return (
    <BaseView>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Space direction="horizontal" align="start">
              <div style={{fontSize: 24}}>
                <AppstoreOutlined />
              </div>
              <div>
                <Space
                  style={{
                    width: '100%',
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <Text type="secondary">Tổng Tenant</Text>
                </Space>
                <Title level={3} style={{margin: 0}}>
                  <h3>
                    {isLoadingStats ? (
                      <div>
                        <Spin />
                      </div>
                    ) : (
                      statsData && <span>{statsData.totalTenant}</span>
                    )}
                  </h3>
                </Title>
              </div>
              <div className="mock-block">
                <AntdTooltip
                  title="Thống kê tổng số Tenant trong hệ thống"
                >
                  <InfoCircleOutlined />
                </AntdTooltip>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Space direction="horizontal" align="start">
              <div style={{fontSize: 24}}>
                <CalendarOutlined />
              </div>
              <div>
                <Space>
                  <Text type="secondary">Tổng Ứng Dụng</Text>
                  <AntdTooltip
                    title="Thống kê tổng số Ứng Dụng trong hệ thống"
                    style={{marginLeft: "auto"}}
                  >
                    <InfoCircleOutlined />
                  </AntdTooltip>
                </Space>
                <Title level={3} style={{margin: 0}}>
                  <h3>
                    {isLoadingStats ? (
                      <div>
                        <Spin />
                      </div>
                    ) : (
                      statsData && <span>{statsData.totalApplication}</span>
                    )}
                  </h3>
                </Title>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Space direction="horizontal" align="start">
              <div style={{fontSize: 24}}>
                <UserOutlined />
              </div>
              <div>
                <Space>
                  <Text type="secondary">Tổng Người Dùng</Text>
                  <AntdTooltip
                    title="Thống kê tổng số Người Dùng trong hệ thống"
                    style={{marginLeft: "auto"}}
                  >
                    <InfoCircleOutlined />
                  </AntdTooltip>
                </Space>
                <Title level={3} style={{margin: 0}}>
                  <h3>
                    {isLoadingStats ? (
                      <div>
                        <Spin />
                      </div>
                    ) : (
                      statsData && <span>{statsData.totalAccount}</span>
                    )}
                  </h3>
                </Title>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Space direction="horizontal" align="start">
              <div style={{fontSize: 24}}>
                <DollarOutlined />
              </div>
              <div>
                <Space>
                  <Text type="secondary">Doanh thu</Text>
                  <AntdTooltip
                    title="Thống kê tổng doanh thu trong hệ thống"
                    style={{marginLeft: "auto"}}
                  >
                    <InfoCircleOutlined />
                  </AntdTooltip>
                </Space>
                <Title level={3} style={{margin: 0}}>
                  <h3>
                    {isLoadingStats ? (
                      <div>
                        <Spin />
                      </div>
                    ) : (
                      statsData && (
                        <span>
                          {statsData.totalRevenue || <span>0</span>} VNĐ
                        </span>
                      )
                    )}
                  </h3>
                </Title>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* <Card style={{ marginTop: 24 }} title="Hoạt động gần đây">
        <List
          itemLayout="horizontal"
          dataSource={activities}
          renderItem={(activity) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <span
                    style={{
                      width: 10,
                      height: 10,
                      backgroundColor: activity.color,
                      borderRadius: '50%',
                      display: 'inline-block',
                      marginTop: 8,
                    }}
                  />
                }
                title={<Text>{activity.text}</Text>}
                description={<Text type="secondary">{activity.time}</Text>}
              />
            </List.Item>
          )}
        />
      </Card> */}
      <DashboardCharts />
    </BaseView>
  );
};
