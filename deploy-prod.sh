now="$(date +'%Y-%m-%d %H:%M:%S')"
message="update $now"
git add .
git commit -m "$message"
git push

yarn build:prod
zip -r build.zip build
scp ./build.zip root@*************:/opt/luanlt/front-end/

ssh root@************* "
    cd /opt/luanlt/front-end/;
    rm -rf admin.wincrypto.ai;
    rm -rf build;
    unzip build.zip;
    mv build admin.wincrypto.ai;
    rm -f build.zip;
    exit;
"

rm -f ./build.zip
