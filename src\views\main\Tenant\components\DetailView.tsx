import React, {useEffect, useState} from "react";
import {
  Drawer,
  Typography,
  Descriptions,
  Divider,
  Tag,
  Space,
  Input,
  Button,
  Row,
  Col,
} from "antd";
import {CloseOutlined, CopyOutlined} from "@ant-design/icons";
import useTenant from "~/hooks/tenant/useTenant";
import {TenantClient} from "./TenantClient";
// import toast service
import {toastService} from "~/services";

const {Text} = Typography;

const DetailView = ({openDrawer, setOpenDrawer, data}) => {
  const {getDetails, details, isDetailLoading} = useTenant({
    pageIndex: 1,
    pageSize: 10,
    tenantId: data?.id,
  });
  const [detailData, setDetailData] = useState<any>(null);

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toastService.success("Copied");
    } catch (err) {}
  };

  useEffect(() => {
    if (data?.id) {
      getDetails({tenantId: data?.id});
    }
  }, [data]);

  useEffect(() => {
    if (details) {
      setDetailData(details);
    }
  }, [details, isDetailLoading]);

  return (
    <Drawer
      title="Chi tiết đối tác"
      width={1000}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      destroyOnClose
      maskClosable={false}
      closeIcon={<CloseOutlined />}
    >
      {isDetailLoading ? (
        <div>Loading...</div>
      ) : (
        <Space direction="vertical" style={{width: "100%"}}>
          <Descriptions
            bordered
            size="small"
            column={2}
            labelStyle={{fontWeight: "bold", width: 150}}
          >
            <Descriptions.Item label="Domain">
              {detailData?.domain}
            </Descriptions.Item>
            <Descriptions.Item label="Tên đối tác">
              {detailData?.name}
            </Descriptions.Item>
            <Descriptions.Item label="Tài khoản">
              {detailData?.rootUsername}
            </Descriptions.Item>
            <Descriptions.Item label="Tên đầy đủ">
              {detailData?.rootFullName}
            </Descriptions.Item>
            <Descriptions.Item label="Mã số thuế">
              {detailData?.taxCode}
            </Descriptions.Item>
            <Descriptions.Item label="Số điện thoại">
              {detailData?.phone}
            </Descriptions.Item>
            <Descriptions.Item label="Email">
              {detailData?.email}
            </Descriptions.Item>
            <Descriptions.Item label="Website">
              {detailData?.website}
            </Descriptions.Item>
            <Descriptions.Item label="Trạng thái">
              <Tag>{detailData?.status}</Tag>
            </Descriptions.Item>
          </Descriptions>

          <Divider />
          <Text strong>Thông tin ứng dụng</Text>
          {detailData?.applications?.map((app) => (
            <div
              key={app.code}
              style={{
                marginBottom: 16,
                paddingBottom: 8,
              }}
            >
              <Descriptions>
                <Descriptions.Item label="Mã ứng dụng">
                  {app.code}
                </Descriptions.Item>
                <Descriptions.Item label="Tên ứng dụng">
                  {app.name}
                </Descriptions.Item>
                <Descriptions.Item label="Phiên bản">
                  v.{app.version}
                </Descriptions.Item>
                <Descriptions.Item label="Trạng thái">
                  <Tag color={app.isActive === true ? "green" : "red"}>
                    {app.isActive === true ? "Hoạt động" : "Không hoạt động"}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Ngày tạo">
                  {app.createdDate
                    ? new Date(app.createdDate).toLocaleString("vi-VN")
                    : "Chưa có"}
                </Descriptions.Item>
              </Descriptions>
              {app.redirectUris && app.redirectUris.length > 0 && (
                <Descriptions column={1} style={{marginTop: 16}} title="Danh sách Uri">
                  {app.redirectUris.map((uri, index) => (
                    <Descriptions.Item key={index} label={`Redirect URI ${index + 1}`}>
                       <Text>{uri}</Text>
                       {/* Copy Button */}
                        <Button
                          size="small"
                          type="default"
                          icon={<CopyOutlined />}
                          onClick={() => handleCopy(uri)}
                          style={{marginLeft: 'auto'}}
                        />
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              )}
              {app.linkLogins && app.linkLogins.length > 0 && (
                <Descriptions column={1} style={{marginTop: 16}} title="Danh sách Logins">
                  {app.linkLogins.map((login, index) => (
                    <Descriptions.Item key={index} label={`Link Login ${index + 1}`}>
                       <Text>{login}</Text>
                       {/* Copy Button */}
                        <Button
                          
                          size="small"
                          type="default"
                          icon={<CopyOutlined />}
                          onClick={() => handleCopy(login)}
                          style={{marginLeft: 'auto'}}
                        />
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              )}
              <Divider />
            </div>
          ))}
        </Space>
      )}
    </Drawer>
  );
};

export default DetailView;
