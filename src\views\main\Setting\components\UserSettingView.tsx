import {useState} from "react";
import {Space, Tag, Avatar, Button, Select, Input} from "antd";
import BaseTable from "~/components/BaseTable";
import BaseView from "~/components/BaseView";
import {NSAccount} from "~/common/enums";
import {ListUserAdminDTO} from "~/dto";
import {
  InfoCircleOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import {CreateUser} from "../components/CreateUser";
import useUserAdmin from "~/hooks/account/useUserAdmin";
import { logoAssets } from "~/assets";
const {Option} = Select;

export const UserSettingView: React.FC = () => {
  const [params, setParams] = useState<ListUserAdminDTO>({
    pageIndex: 1,
    pageSize: 20,
    data: [],
    total: 0,
  });
  const [openDrawerCreate, setOpenDrawerCreate] = useState(false);
  const {data, isLoading, inactiveAccount, activeAccount, refetch} =
    useUserAdmin(params);

  const handleCreate = () => {
    setOpenDrawerCreate(true);
  };

  const handleOnChangeSearchValue = (value) => {};
  const handleOnChangeType = () => {};
  const handleFilter = () => {};

  const columns = [
    {
      title: "Avatar",
      dataIndex: "avatar",
      key: "avatar",
      render: (url: string) => {
        if(!url) return <Avatar src={logoAssets.logo} />
        return <Avatar src={url} />
      },
    },
    {
      title: "Username",
      dataIndex: "username",
      key: "username",
    },
    {
      title: "Full Name",
      dataIndex: "fullName",
      key: "fullName",
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (type: NSAccount.EAdminType) => (
        type === NSAccount.EAdminType.SUPER_ADMIN 
        ? <Tag color='blue'>{type}</Tag>
        : <Tag color='blue-inverse'>{type}</Tag>
        
      ),
    },
    {
      title: "Created Date",
      dataIndex: "createdDate",
      key: "createdDate",
    },
  ];

  const columnsAction = [
    {
      title: "Action",
      render: () => (
        <Space>
          <Button icon={<InfoCircleOutlined />} type="primary"></Button>
          <Button icon={<EditOutlined />}></Button>
        </Space>
      ),
    },
  ];

  const accounts = data?.pages?.[0]?.data || [];
  const total = data?.pages?.[0]?.total || 0;

  return (
    <BaseView>
      <CreateUser
        openDrawer={openDrawerCreate}
        setOpenDrawer={(e) => setOpenDrawerCreate(e)}
      />
      <Space
        style={{
          width: "100%",
          justifyContent: "space-between",
          marginBottom: 12,
        }}
      >
        {/* Search account */}
        <Space>
          <Input
            onChange={(e) => handleOnChangeSearchValue(e.target.value)}
            placeholder="Tìm kiếm tài khoản..."
            prefix={<SearchOutlined />}
            style={{width: 250}}
          />
          {/* Loại tài khoản */}
          <Button icon={<FilterOutlined />} onClick={handleFilter}>
            Lọc
          </Button>
        </Space>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
          Thêm tài khoản
        </Button>
      </Space>
      <BaseTable
        isLoading={false}
        total={total}
        data={accounts}
        columns={[...columns, ...columnsAction]}
      />
    </BaseView>
  );
};
