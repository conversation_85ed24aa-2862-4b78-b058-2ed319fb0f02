import BaseView from "~/components/BaseView";
import React, { useState, useEffect } from "react";
import SelectTenant from "~/components/ApiComponents/SelectTenant";
import BaseTable from "~/components/BaseTable";
import {
  Row,
  Col,
  Card,
  Statistic,
  DatePicker,
  Space,
  Button,
  Tag,
  Alert,
} from "antd";
import { SearchOutlined } from "@ant-design/icons";
import useTenant, {
  TenantAccount,
  TenantResponse,
} from "~/hooks/tenant/useTenant";

interface RevenuaProps {}

export const Revenua = () => {
  // State to manage selected tenant
  const [selectedTenant, setSelectedTenant] = useState<string | undefined>(
    undefined
  );
  const { getInfoTenant, isGetInfo } = useTenant();

  const [tenantAccount, setTenantAccount] = useState<TenantResponse>({});
  const [pageSize, setPageSize] = useState<number>(10);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [createdDateFrom, setCreatedDateFrom] = useState<Date>();
  const [createdDateTo, setCreatedDateTo] = useState<Date>();

  useEffect(() => {
    loadTenantAccount();
  }, [selectedTenant, pageSize, pageIndex]);

  const columns = [
    {
      title: "STT",
      key: "stt",
      width: 50,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: "Tên tài khoản",
      dataIndex: "username",
      key: "username",
    },
    {
      title: "Họ và tên",
      dataIndex: "fullName",
      key: "fullName",
    },
    // {
    //   title: "Email",
    //   dataIndex: "email",
    //   key: "email",
    // },
    // {
    //   title: "Số điện thoại",
    //   dataIndex: "phone",
    //   key: "phone",
    // },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (status: string) =>
        status === "ACTIVE" ? (
          <Tag color="green">Hoạt động</Tag>
        ) : (
          <Tag color="red">Không hoạt động</Tag>
        ),
    },
    // Lần đăng nhập gần nhất
    // {
    //   title: "Lần đăng nhập gần nhất",
    //   dataIndex: "createdDate",
    //   key: "createdDate",
    //   render: (text: string) => new Date(text).toLocaleString("vi-VN"),
    // },
    // {
    //   title: "Số lần đăng nhập",
    //   dataIndex: "numberLogin",
    //   key: "numberLogin",
    // },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (text: string) => new Date(text).toLocaleString("vi-VN"),
    },
  ];

  const loadTenantAccount = () => {
    if (selectedTenant) {
      getInfoTenant({
        tenantId: selectedTenant,
        pageSize: pageSize,
        pageIndex: pageIndex,
        ...(createdDateFrom && {
          createdDateFrom: createdDateFrom.toISOString(),
        }),
        ...(createdDateTo && {
          createdDateTo: createdDateTo.toISOString(),
        }),
      }).then((res: TenantResponse) => {
        setTenantAccount(res);
      });
    }
  };

  const handleOnChangeTenant = (value) => {
    console.log("Selected tenant:", value);
    setSelectedTenant(value);
  };

  const handlePageSizeChange = (pageIndex: number, pageSize: number) => {
    setPageSize(pageSize);
    setPageIndex(pageIndex);
  };

  return (
    <BaseView>
      {/* Select Tenant */}
      <Space>
        <SelectTenant onChange={handleOnChangeTenant} />
        {selectedTenant && (
          <>
            <DatePicker.RangePicker
              onChange={(value: any) => {
                if (!value) {
                  setCreatedDateFrom(undefined);
                  setCreatedDateTo(undefined);
                  return;
                }
                setCreatedDateFrom(value[0]);
                setCreatedDateTo(value[1]);
              }}
            />
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={loadTenantAccount}
            />
          </>
        )}
        {!selectedTenant && (
          <Alert type="warning" message="Chọn Tenant để xem báo cáo" banner />
        )}
      </Space>

      {selectedTenant && (
        <div>
          {/* Thống kê số lượng tài đang hoạt động và ngưng hoạt động */}
          <Row gutter={[16, 16]} style={{ marginTop: 12, marginBottom: 12 }}>
            <Col span={8}>
              <Card style={{ backgroundColor: "#ffd166" }}>
                <Statistic
                  title="Tổng số lượng tài khoản"
                  value={tenantAccount.totalAccount}
                  suffix="tài khoản"
                  valueStyle={{ color: "#000" }}
                />
              </Card>
            </Col>

            <Col span={8}>
              <Card style={{ backgroundColor: "#08d750ff" }}>
                <Statistic
                  title="Đang hoạt động"
                  value={tenantAccount.activeAccount}
                  suffix="tài khoản"
                />
              </Card>
            </Col>

            <Col span={8}>
              <Card style={{ backgroundColor: "#ff0000df" }}>
                <Statistic
                  title="Ngưng hoạt động"
                  value={tenantAccount.inActiveAccount}
                  suffix="tài khoản"
                />
              </Card>
            </Col>

            {/* <Col span={6}>
              <Card style={{ backgroundColor: "#13ade0ff" }}>
                <Statistic
                  title={`Hoạt động trong tháng ${
                    parseInt(new Date().getMonth().toLocaleString("vi-VN")) + 1
                  }/${new Date().getFullYear()}`}
                  value={tenantAccount.activeThisMonth}
                  suffix="tài khoản"
                />
              </Card>
            </Col> */}
          </Row>
          {/* Danh sach account còn hoạt động của 1 Tenant */}
          <BaseTable
            columns={columns}
            data={tenantAccount.tenantInfos}
            total={tenantAccount?.tenantInfos?.length}
            isLoading={isGetInfo}
            onPageChange={handlePageSizeChange}
          />
        </div>
      )}
    </BaseView>
  );
};
