.layout {
  height: 100vh;
  display: flex; /* để justify + align hoạt động */
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-color: white; /* COLORS.WHITE */
}

.content {
    background-color: #fcad16; /* COLORS.PRIMARY */
  display: flex; /* để justify + align hoạt động */
  justify-content: center;
  align-items: center;
}

.form-container {
  background-color: white; /* COLORS.WHITE */
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 360px;
  color: black; /* COLORS.BLACK */
}

.title {
  text-align: center;
  margin-bottom: 24px;
  color: #1890ff; /* COLORS.PRIMARY_2 */
  font-size: 30px;
}

.submit-button {
  background-color: #fcad16; /* COLORS.PRIMARY */
  border-color: #fcad16; /* COLORS.PRIMARY */
  width: 100%;
  height: 40px;
  color: white;
  cursor: pointer;
}

.submit-button:hover {
  background-color: white;
  border-color: white;
  color: #fcad16; /* để chữ đổi màu khi hover */
}

.form-item {
  margin-bottom: 16px;
}
